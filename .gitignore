# Dependencies (don't upload to GitHub)
resources/ffmpeg/
resources/yt-dlp/
ffmpeg.exe
yt-dlp.exe

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
*.exe
*.msi
*.dmg
*.deb
*.rpm
*.app

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# User data
downloads/
output/
