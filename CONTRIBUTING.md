# Contributing to <PERSON>id<PERSON>uncher

**Help Make This Thing Less Broken**

Thanks for considering contributing to Vid<PERSON>uncher! I'm just a person who got frustrated with video codecs, not a real developer, so any help is genuinely appreciated.

## How You Can Help

### Found a Bug? (You Probably Did)
- **Check existing issues** first - someone might have already reported it
- **Create a new issue** with details about what broke
- **Include your setup** - Windows version, Python version, what you were trying to do
- **Be patient** - I'll get to it when I can figure out what went wrong

### Have an Idea?
- **Open an issue** to discuss it first
- **Keep it simple** - remember, this is a tool born from frustration, not a enterprise solution
- **Consider the scope** - VidMuncher does one thing: download and convert videos

### Want to Fix Something?
Even better! Here's how:

## Getting Started

### Prerequisites
- Windows 10/11 (because that's what I test on)
- Python 3.8+ 
- Git (obviously)
- Patience with my code

### Setup for Development
```bash
# Fork the repo on GitHub first

# Clone your fork
git clone https://github.com/yourusername/VidMuncher.git
cd VidMuncher

# Install dependencies
pip install -r requirements.txt

# Set up the bin folder (see README.md for details)
# Download FFmpeg and yt-dlp to bin/

# Run it to make sure it works
python vidmuncher.py
```

## Code Guidelines

### The Reality Check
- **I'm not a professional developer** - so don't expect enterprise-level code
- **Keep it simple** - if I can't understand it, I can't maintain it
- **Comment your code** - future me will thank you
- **Test your changes** - at least make sure it doesn't crash immediately

### Code Style
- **Follow existing patterns** - consistency matters more than perfection
- **Use descriptive names** - `download_video()` is better than `dv()`
- **Handle errors gracefully** - users shouldn't see cryptic Python tracebacks
- **Debug prints are fine** - they help when things go wrong

### File Structure
```
VidMuncher/
├── vidmuncher.py      # Main entry point
├── gui.py             # GUI components
├── downloader.py      # yt-dlp integration
├── encoder.py         # FFmpeg encoding
├── utils.py           # Helper functions
├── config.py          # Configuration and constants
└── build_vidmuncher.py # Build script
```

## Making Changes

### Before You Start
1. **Create an issue** to discuss major changes
2. **Fork the repository**
3. **Create a feature branch** - `git checkout -b fix-something-broken`

### While You Work
- **Test frequently** - make sure you didn't break anything
- **Keep commits focused** - one fix per commit
- **Write commit messages** that explain what you did

### When You're Done
1. **Test the build process** - `python build_vidmuncher.py`
2. **Update documentation** if needed
3. **Create a pull request** with a clear description

## Pull Request Guidelines

### What Makes a Good PR
- **Clear description** - what does it fix/add?
- **Small scope** - easier to review and merge
- **Working code** - it should actually run
- **No breaking changes** - unless absolutely necessary

### What I'll Check
- **Does it work?** - the most important question
- **Is it understandable?** - can I maintain this?
- **Does it fit?** - is it within VidMuncher's scope?
- **Is it tested?** - at least manually

## Types of Contributions

### Bug Fixes
Always welcome! Especially:
- Crashes and error handling
- UI improvements
- Encoding issues
- File handling problems

### New Features
Think twice, then think again:
- **Keep it simple** - VidMuncher isn't trying to be everything
- **Consider maintenance** - can I maintain this long-term?
- **User benefit** - does this solve a real problem?

### Documentation
Super helpful:
- Fix typos and unclear instructions
- Add examples
- Improve setup guides
- Update outdated information

## What Not to Contribute

### Please Don't
- **Rewrite everything** - incremental improvements are better
- **Add complex dependencies** - keep it lightweight
- **Change the core purpose** - it's a video downloader, not a video editor
- **Make it "enterprise ready"** - it's a personal tool that others can use

## Questions?

### Getting Help
- **Check existing issues** first
- **Read the code** - it's not that complicated
- **Ask in an issue** - I'll try to help
- **Be specific** - "it doesn't work" isn't helpful

### Communication
- **Be patient** - this is a side project
- **Be kind** - we're all learning
- **Be realistic** - I'm not going to rewrite everything

## Recognition

### Contributors
Everyone who helps will be mentioned in the README (if they want to be). You're making this tool better for everyone who's frustrated with video codecs.

### Code of Conduct
**Be a decent human being.** That's it. Don't be rude, don't be a jerk, and remember we're all just trying to make something useful.

---

**Thanks for helping make VidMuncher less broken!**

*Remember: Perfect is the enemy of good enough, and good enough is what we're aiming for here.*
