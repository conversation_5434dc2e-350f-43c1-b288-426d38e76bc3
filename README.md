# VidMuncher

**A Video Downloader Born from Pure Frustration**

[![Made by Non-Developer](https://img.shields.io/badge/Made%20by-Non--Developer-red.svg)]()

VidMuncher exists because I got tired of wrestling with video codecs that certain popular video editing software *cough* refuses to play nice with. I'm not a real developer - just someone who got fed up with the "codec not supported" dance and decided to do something about it.

## The Origin Story (AKA My Codec Nightmare)

Picture this: You download a perfectly good video from YouTube, import it into your fancy video editor, and BAM! "Codec not supported" or "Media offline" or whatever cryptic error message they feel like throwing at you today. Then you spend 2 hours trying different converters, most of which either don't work, cost money, or come with enough malware to power a small botnet.

So I thought, "How hard could it be to make a simple tool that just downloads videos and converts them to H.264?" Spoiler alert: It was harder than I thought, but here we are. This tool was born from pure spite and caffeine, built by someone who barely knows what a function is but was determined to never see "codec not supported" again.

## What This Thing Actually Does

- **Video Download** - Grabs videos from YouTube and friends (thanks to the heroes at yt-dlp who did the real work)
- **H.264 Encoding** - Converts your videos to a format that won't make your video editor throw a tantrum
- **Audio Extraction** - Sometimes you just want the audio, and that's valid
- **Dark Theme GUI** - Because light themes are for psychopaths
- **Smart File Naming** - Handles weird characters and duplicates so you don't have to think about it
- **GPU Acceleration** - Uses your fancy graphics card if you have one (and if it feels like cooperating)

## What You'll Need

- Windows 10/11 (because I'm too lazy to test on other platforms)
- Python 3.8+ (if you're building from source and enjoy pain)
- FFmpeg and yt-dlp (already included because I'm not completely heartless)

## Getting This Thing Running

### Option 1: The Easy Way (For Normal People)
1. Download the latest release (look for the big green button)
2. Extract it somewhere you won't forget
3. Double-click `VidMuncher.exe` and pray it works

### Option 2: The Hard Way (For Masochists)
```bash
# Clone this mess
git clone https://github.com/aprixlabs/VidMuncher.git
cd VidMuncher

# Install a bunch of Python stuff
pip install -r requirements.txt

# Download FFmpeg (Windows 64-bit)
# Go to: https://www.gyan.dev/ffmpeg/builds/
# Download "release builds"
# Extract and copy ffmpeg.exe to bin/ffmpeg.exe

# Download yt-dlp (Windows 64-bit)
# Go to: https://github.com/yt-dlp/yt-dlp/releases/latest
# Download "yt-dlp.exe" and put it in bin/yt-dlp.exe

# The bin folder should contain:
# bin/
# ├── ffmpeg.exe
# └── yt-dlp.exe

# Cross your fingers and run it
python vidmuncher.py
```

### Manual Setup (If You Like Doing Things the Hard Way)

**Step 1: Download FFmpeg**
1. Go to [FFmpeg Builds by Gyan](https://www.gyan.dev/ffmpeg/builds/)
2. Download "release builds"
3. Extract the zip file
4. Copy `ffmpeg.exe` from the extracted folder to `bin/ffmpeg.exe`

**Step 2: Download yt-dlp**
1. Go to [yt-dlp Releases](https://github.com/yt-dlp/yt-dlp/releases/latest)
2. Download `yt-dlp.exe` (Windows 64-bit)
3. Put it in `bin/yt-dlp.exe`

**Step 3: Verify Your Setup**
Your project folder should look like this:
```
VidMuncher/
├── bin/                    (folder already included)
│   ├── ffmpeg.exe         (you download this)
│   └── yt-dlp.exe         (you download this)
├── assets/
├── gui.py
├── vidmuncher.py
└── ... (other files)
```

### Building Your Own Executable (Advanced Masochism)
```bash
# Make sure you have the bin/ folder set up first!
# Then build the thing
python build_vidmuncher.py

# Clean up the mess when it inevitably breaks
python build_vidmuncher.py clean
```

## How to Use This Contraption

1. **Paste a Video URL** - Copy that YouTube link you've been hoarding
2. **Hit "Analyze"** - Watch it magically grab video info (when it works)
3. **Pick Quality** - Choose wisely, your storage space depends on it
4. **Select Where to Save** - Pick a folder that actually exists
5. **Click "Download"** - Go make coffee, this might take a while

### What Sites Work?
Basically anything yt-dlp supports, which is pretty much everything except the sites you actually want to download from. But seriously, it works with:
- YouTube (obviously)
- TikTok (for the short attention span folks)
- Instagram (for the influencer wannabes)
- And like 500+ other sites you've never heard of

## The Nerdy Stuff (Skip if You Just Want It to Work)

### H.264 Encoding Magic
- Tries to use your GPU first (NVIDIA, AMD, Intel - I don't discriminate)
- Falls back to CPU when your GPU decides to be difficult
- Progress bar that sometimes lies but the encoding still works (don't ask me why)

### File Handling Wizardry
- Automatically fixes filenames because special characters are the devil
- Adds numbers to duplicates so you don't accidentally overwrite your masterpiece
- Cleans up temporary files (most of the time)

## Want to Help? (Please Do)

I'm just a person who got frustrated with video codecs, not a real developer. So if you:
- Find bugs (you will)
- Have ideas for improvements
- Actually know how to code properly
- Want to fix my terrible code

Please contribute! I promise I won't judge your pull requests as harshly as you'll judge my code.

## Legal Stuff (The Boring Part)

This is for personal use only. Don't be that person who:
- Downloads copyrighted content they shouldn't
- Ignores platform terms of service
- Blames me when they get in trouble

Use your brain and don't do anything stupid.

## License

MIT License - Do whatever you want with this code, just don't blame me when it breaks. See [LICENSE](LICENSE) for the legal mumbo jumbo.

## What's Under the Hood (For the Curious)

Built with whatever I could figure out how to use:
- **Python 3.8+** - Because I know just enough to be dangerous
- **Tkinter** - The GUI framework nobody loves but everyone uses
- **FFmpeg** - The Swiss Army knife of video processing (doing the real work)
- **yt-dlp** - The actual hero that downloads everything
- **PyInstaller** - Turns my Python mess into a somewhat functional executable

## How It's Built (Spoiler: It's a Mess)

- **Modular Design** - I split things into files so I could pretend I know what I'm doing
- **Threaded Operations** - So the UI doesn't freeze and make you think it crashed
- **Resource Management** - Assets are bundled because external files are annoying
- **Windows-Only** - Because testing on multiple platforms is hard

## Thanks To

- **FFmpeg Team** - For making video processing possible for mere mortals
- **yt-dlp Developers** - For doing the impossible and making it look easy
- **Stack Overflow** - For answering questions I didn't know I had
- **My Video Editor** - For being so picky about codecs that I had to build this thing
