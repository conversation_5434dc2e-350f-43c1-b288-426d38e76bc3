# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['vidmuncher.py'],
    pathex=[],
    binaries=[],
    datas=[('assets/header.png', 'assets'), ('assets/icon.ico', 'assets'), ('assets/icon.png', 'assets')],
    hiddenimports=['PIL._tkinter_finder', 'PIL.Image', 'PIL.ImageTk', 'tkinter', 'tkinter.ttk', 'tkinter.filedialog', 'tkinter.messagebox', 'requests', 'pathlib', 'glob', 'shutil', 're', 'time', 'os', 'sys', 'getpass', 'subprocess', 'threading', 'json'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['yt_dlp', 'ffmpeg'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='VidMuncher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.py',
    icon=['assets\\icon.ico'],
)
