#!/usr/bin/env python3
"""
Build script to compile Vid<PERSON>uncher to executable
With yt-dlp and ffmpeg as external files (not bundled)
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

def build_vidmuncher():
    """Build VidMuncher.exe with external dependencies"""

    print("VidMuncher Build Script")
    print("=" * 50)

    # Path to main file
    main_file = "vidmuncher.py"
    exe_name = "VidMuncher"

    # Check required files
    if not os.path.exists(main_file):
        print(f"Error: {main_file} not found")
        return False

    if not os.path.exists("assets"):
        print("Error: assets folder not found")
        return False

    print(f"Working directory: {os.getcwd()}")
    print(f"Building {exe_name}.exe...")
    
    # PyInstaller command dengan external dependencies
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--windowed",                   # No console window (GUI app)
        "--name", exe_name,             # Nama executable
        "--distpath", "dist",           # Output directory
        "--workpath", "build",          # Build directory
        "--specpath", ".",              # Spec file location
        "--clean",                      # Clean cache
        "--noconfirm",                  # Overwrite without asking
    ]

    # Add version info if file exists
    if os.path.exists("version_info.py"):
        cmd.extend(["--version-file", "version_info.py"])
        print("Adding version metadata to executable")

    # Add icon if available
    icon_files = ["assets/icon.ico", "assets/icon.png"]
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            if icon_file.endswith('.ico'):
                cmd.extend(["--icon", icon_file])
                print(f"Using icon: {icon_file}")
            break

    # Bundle assets into executable
    print("Bundling assets into executable...")
    assets_to_bundle = ["header.png", "icon.ico", "icon.png"]

    for asset_file in assets_to_bundle:
        asset_path = f"assets/{asset_file}"
        if os.path.exists(asset_path):
            cmd.extend(["--add-data", f"{asset_path};assets"])
            print(f"Bundling: {asset_file}")
        else:
            print(f"Warning: Asset not found: {asset_file}")
    
    # Hidden imports for dependencies
    hidden_imports = [
        "PIL._tkinter_finder",
        "PIL.Image",
        "PIL.ImageTk",
        "tkinter",
        "tkinter.ttk",
        "tkinter.filedialog",
        "tkinter.messagebox",
        "requests",
        "pathlib",
        "glob",
        "shutil",
        "re",
        "time",
        "os",
        "sys",
        "getpass",
        "subprocess",
        "threading",
        "json"
    ]

    for imp in hidden_imports:
        cmd.extend(["--hidden-import", imp])

    # Exclude yt-dlp and ffmpeg from bundle
    cmd.extend([
        "--exclude-module", "yt_dlp",
        "--exclude-module", "ffmpeg"
    ])
    
    # Add main file
    cmd.append(main_file)

    print("Running PyInstaller...")
    print()

    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True)

        # Check build result
        exe_path = f"dist/{exe_name}.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB

            print()
            print("Build successful!")
            print(f"Executable: {exe_path}")
            print(f"Size: {file_size:.1f} MB")

            # Copy external dependencies
            setup_external_deps()

            print()
            print("Build complete")
            
            return True
        else:
            print("Build failed - executable not found")
            return False

    except subprocess.CalledProcessError as e:
        print(f"Build failed with error code: {e.returncode}")
        return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def setup_external_deps():
    """Setup external dependencies"""
    print("Setting up external dependencies...")

    # Create bin directory in dist
    dist_bin = Path("dist/bin")
    dist_bin.mkdir(exist_ok=True)

    # Copy entire bin folder contents (including licenses)
    source_bin = Path("bin")

    if source_bin.exists() and source_bin.is_dir():
        print("Copying bin folder contents...")

        # Copy all files from bin folder
        for item in source_bin.iterdir():
            if item.is_file():
                dest_file = dist_bin / item.name
                shutil.copy2(item, dest_file)

                # Show file size for executables
                if item.suffix.lower() == '.exe':
                    file_size = item.stat().st_size / (1024 * 1024)  # MB
                    print(f"Copied: {item.name} ({file_size:.1f} MB)")
                else:
                    print(f"Copied: {item.name}")

        # Verify critical executables
        critical_files = ["yt-dlp.exe", "ffmpeg.exe", "ffprobe.exe"]
        for critical_file in critical_files:
            if not (dist_bin / critical_file).exists():
                print(f"ERROR: {critical_file} not found in bin folder")
    else:
        print("ERROR: bin/ folder not found")

    # Copy license files for legal compliance
    license_files = ["LICENSE.txt", "FFMPEG_LICENSE.txt", "YT-DLP_LICENSE.txt", "THIRD_PARTY_NOTICES.txt", "README.txt"]

    for license_file in license_files:
        if os.path.exists(license_file):
            dest_file = Path("dist") / license_file
            shutil.copy2(license_file, dest_file)
            print(f"Copied: {license_file}")

def clean_build():
    """Clean build artifacts"""
    print("Cleaning build artifacts...")

    dirs_to_clean = ["build", "dist", "__pycache__"]

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Removed: {dir_name}/")

    # Remove spec files
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"Removed: {spec_file}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_build()
    else:
        success = build_vidmuncher()

        if success:
            print("\nBuild completed successfully")
        else:
            print("\nBuild failed - check error messages above")
