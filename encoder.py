"""
VidMuncher Encoder Module
Contains FFmpeg encoding logic and encoder detection functionality
"""

import re
import subprocess
import threading
from config import (
    DEBUG_MODE,
    FFMPEG_PATH,
    ENCODERS_CONFIG,
    ENCODER_TEST_TIMEOUT,
    AUDIO_CODEC,
    AUDIO_BITRATE,
    FFMPEG_COMMON_ARGS
)
from utils import debug_print, find_downloaded_file

class EncoderManager:
    """Manages FFmpeg encoding operations and encoder detection"""
    
    def __init__(self):
        self.best_encoder = None
        self.active_processes = []
    
    def detect_best_encoder(self):
        """
        Detect best available H264 encoder with GPU priority
        
        Returns:
            dict: Encoder information with encoder, name, hwaccel, and settings
        """
        if self.best_encoder:
            return self.best_encoder
        
        debug_print("Starting encoder detection...")
        
        for encoder_info in ENCODERS_CONFIG:
            try:
                # Test command to check encoder availability
                test_cmd = [str(FFMPEG_PATH)]
                
                # Add hwaccel if available
                if encoder_info["hwaccel"]:
                    test_cmd.extend(["-hwaccel", encoder_info["hwaccel"]])
                
                # Add test input
                test_cmd.extend(encoder_info["test_args"])
                
                # Add encoder
                test_cmd.extend(["-c:v", encoder_info["encoder"]])
                
                # Add encoder-specific test settings
                self._add_encoder_test_settings(test_cmd, encoder_info)
                
                # Output to null device
                test_cmd.extend(["-f", "null", "-"])
                
                debug_print(f"Testing encoder: {encoder_info['name']} ({encoder_info['encoder']})")
                
                # Run test with short timeout
                result = subprocess.run(
                    test_cmd,
                    capture_output=True,
                    text=True,
                    timeout=ENCODER_TEST_TIMEOUT,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                
                if result.returncode == 0:
                    debug_print(f"✅ Encoder available: {encoder_info['name']}")
                    self.best_encoder = encoder_info
                    return encoder_info
                else:
                    debug_print(f"❌ Encoder failed: {encoder_info['name']} - {result.stderr[:100]}")
            
            except subprocess.TimeoutExpired:
                debug_print(f"⏱️ Encoder test timeout: {encoder_info['name']}")
                continue
            except Exception as e:
                debug_print(f"❌ Encoder test error: {encoder_info['name']} - {str(e)}")
                continue
        
        # Fallback to CPU if all fail
        debug_print("⚠️ All GPU encoders failed, using CPU fallback")
        fallback = {
            "encoder": "libx264",
            "name": "CPU (Software)",
            "hwaccel": None,
            "settings": {"preset": "medium", "crf": "23"}
        }
        self.best_encoder = fallback
        return fallback
    
    def _add_encoder_test_settings(self, cmd, encoder_info):
        """Add encoder-specific test settings to command"""
        encoder = encoder_info["encoder"]
        
        if "nvenc" in encoder:
            cmd.extend(["-preset", "p1", "-cq", "30"])
        elif "amf" in encoder:
            cmd.extend(["-quality", "speed", "-qp_i", "30"])
        elif "qsv" in encoder:
            cmd.extend(["-preset", "veryfast", "-global_quality", "30"])
        else:
            cmd.extend(["-preset", "ultrafast", "-crf", "30"])
    
    def detect_input_codec(self, input_file):
        """
        Detect input video codec using ffprobe

        Args:
            input_file (str): Path to input file

        Returns:
            str: Codec name (e.g., 'av01', 'vp09', 'avc1') or None if detection fails
        """
        try:
            # Use ffprobe for accurate codec detection
            ffprobe_path = str(FFMPEG_PATH).replace("ffmpeg.exe", "ffprobe.exe")
            cmd = [
                ffprobe_path,
                "-v", "quiet",
                "-select_streams", "v:0",
                "-show_entries", "stream=codec_name",
                "-of", "csv=p=0",
                input_file
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and result.stdout.strip():
                codec = result.stdout.strip()
                debug_print(f"Input codec detected: {codec}")
                return codec
            else:
                debug_print("Could not detect input codec")
                return None

        except Exception as e:
            debug_print(f"Codec detection failed: {e}")
            return None

    def build_encoding_command(self, input_file, output_file, encoder_info=None):
        """
        Build FFmpeg encoding command with AV1 compatibility

        Args:
            input_file (str): Path to input file
            output_file (str): Path to output file
            encoder_info (dict): Encoder information, if None will detect best

        Returns:
            list: FFmpeg command arguments
        """
        if encoder_info is None:
            encoder_info = self.detect_best_encoder()
        
        cmd = [str(FFMPEG_PATH)]
        
        # Add hardware acceleration if available
        if encoder_info["hwaccel"]:
            cmd.extend(["-hwaccel", encoder_info["hwaccel"]])
        
        # Detect input codec for special handling
        input_codec = self.detect_input_codec(input_file)

        # Input file
        cmd.extend(["-i", input_file])

        # AV1 specific handling (non-intrusive)
        if input_codec and "av01" in input_codec:
            debug_print("⚡ Applying AV1 → H.264 optimizations")
            # Force pixel format conversion for AV1 compatibility (10-bit to 8-bit)
            cmd.extend(["-pix_fmt", "yuv420p"])
            # Color space conversion for HDR AV1 content
            cmd.extend(["-colorspace", "bt709"])
            cmd.extend(["-color_primaries", "bt709"])
            cmd.extend(["-color_trc", "bt709"])
            # Additional buffer for AV1 decoding
            cmd.extend(["-bufsize", "32M"])

            # Force software encoder for 10-bit AV1 content if using hardware encoder
            if encoder_info.get("hwaccel") and "nvenc" in encoder_info["encoder"]:
                debug_print("⚠️ 10-bit AV1 detected, forcing software encoder for compatibility")
                encoder_info = {
                    "encoder": "libx264",
                    "name": "CPU (AV1 10-bit)",
                    "hwaccel": None,
                    "settings": {"preset": "medium", "crf": "20"}
                }

        # Video encoder (existing logic unchanged)
        cmd.extend(["-c:v", encoder_info["encoder"]])
        
        # Add encoder-specific settings
        settings = encoder_info.get("settings", {})
        for key, value in settings.items():
            if key.startswith("qp_"):
                cmd.extend([f"-{key}", value])
            else:
                cmd.extend([f"-{key}", value])
        
        # Audio settings
        cmd.extend(["-c:a", AUDIO_CODEC, "-b:a", AUDIO_BITRATE])
        
        # Common FFmpeg arguments
        cmd.extend(FFMPEG_COMMON_ARGS)
        
        # Output file with overwrite
        cmd.extend(["-y", output_file])
        
        return cmd
    
    def encode_video(self, input_file, output_file, progress_callback=None, cancel_check=None):
        """
        Encode video with progress monitoring
        
        Args:
            input_file (str): Path to input file
            output_file (str): Path to output file
            progress_callback (callable): Function to call with progress updates
            cancel_check (callable): Function to check if encoding should be cancelled
            
        Returns:
            tuple: (success: bool, error_message: str or None)
        """
        try:
            # Detect best encoder
            encoder_info = self.detect_best_encoder()
            encoder_name = encoder_info["name"]
            
            # Build command
            cmd = self.build_encoding_command(input_file, output_file, encoder_info)
            
            debug_print(f"FFmpeg command: {' '.join(cmd)}")
            
            # Start encoding process
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # Track process for cleanup
            self.active_processes.append(process)
            
            # Monitor progress
            duration_seconds = None
            encoding_progress = 0

            for line in process.stdout:
                # Check for cancellation
                if cancel_check and cancel_check():
                    debug_print("FFmpeg encoding cancelled by user")
                    break
                
                line = line.strip()
                debug_print(f"FFmpeg: {line}")
                
                # Extract duration
                if "Duration:" in line and not duration_seconds:
                    duration_seconds = self._extract_duration(line)
                    if duration_seconds:
                        debug_print(f"Video duration: {duration_seconds} seconds")
                
                # Extract progress
                elif "time=" in line and duration_seconds:
                    progress = self._extract_progress(line, duration_seconds)
                    if progress is not None and progress > encoding_progress:
                        encoding_progress = progress
                        if progress_callback:
                            progress_callback(f"Encoding H264 ({encoder_name}) - {progress:.1f}%", progress)
                
                # Fallback progress for cases where duration is not detected
                elif encoding_progress == 0 and any(keyword in line.lower() for keyword in ['frame=', 'fps=', 'bitrate=']):
                    encoding_progress = min(50, encoding_progress + 10)
                    if progress_callback:
                        progress_callback(f"Encoding H264 ({encoder_name}) - {encoding_progress}%", encoding_progress)
            
            # Wait for completion
            process.wait()

            # Remove from active processes
            if process in self.active_processes:
                self.active_processes.remove(process)

            debug_print(f"FFmpeg process finished with return code: {process.returncode}")

            # Debug: Check cancellation status
            is_cancelled = cancel_check and cancel_check()
            debug_print(f"Is cancelled check: {is_cancelled}")

            if process.returncode == 0:
                debug_print("FFmpeg encoding completed successfully")
                return True, None
            else:
                # Check if this is a cancellation
                if cancel_check and cancel_check():
                    debug_print("H264 encoding was cancelled by user")
                    return False, "Encoding was cancelled by user"
                else:
                    # Check if this is AV1-related failure
                    input_codec = self.detect_input_codec(input_file)
                    if input_codec == "av01":
                        debug_print("⚠️ AV1 encoding failed, trying software fallback...")

                        # Try software-only approach for AV1
                        fallback_encoder = {
                            "encoder": "libx264",
                            "name": "CPU (AV1 Fallback)",
                            "hwaccel": None,  # Force software
                            "settings": {"preset": "medium", "crf": "23"}
                        }

                        # Retry with software encoder (prevent infinite recursion)
                        if encoder_info.get("hwaccel") is not None:  # Only retry if was using hardware
                            return self.encode_video(input_file, output_file, progress_callback, cancel_check, fallback_encoder)

                    # Regular error handling (existing logic)
                    if process.returncode == 4294967294:
                        error_msg = "H264 Encoding Failed! (Process terminated unexpectedly)"
                        debug_print(f"FFmpeg terminated unexpectedly with return code: {process.returncode}")
                    else:
                        error_msg = f"FFmpeg failed with return code: {process.returncode}"
                        debug_print(error_msg)

                        # Add AV1-specific error message
                        if input_codec == "av01":
                            error_msg += "\nAV1 codec requires significant processing power."
                            error_msg += "\nTry using a lower quality preset or disable H.264 encoding."

                    return False, error_msg
        
        except Exception as e:
            error_msg = f"Encoding error: {str(e)}"
            debug_print(error_msg)
            return False, error_msg
    
    def _extract_duration(self, line):
        """Extract video duration from FFmpeg output"""
        try:
            duration_match = re.search(r'Duration: (\d+):(\d+):(\d+)\.(\d+)', line)
            if duration_match:
                h, m, s, ms = map(int, duration_match.groups())
                return h * 3600 + m * 60 + s + ms / 100
        except:
            pass
        return None
    
    def _extract_progress(self, line, duration_seconds):
        """Extract encoding progress from FFmpeg output"""
        try:
            time_match = re.search(r'time=(\d+):(\d+):(\d+)\.(\d+)', line)
            if time_match:
                h, m, s, ms = map(int, time_match.groups())
                current_seconds = h * 3600 + m * 60 + s + ms / 100
                progress = min(99, (current_seconds / duration_seconds) * 100)
                return progress
        except:
            pass
        return None
    
    def cleanup_processes(self):
        """Clean up active encoding processes"""
        for process in self.active_processes[:]:
            try:
                if process.poll() is None:  # Process still running
                    process.terminate()
                    process.wait(timeout=5)
                self.active_processes.remove(process)
            except:
                pass
    
    def cancel_encoding(self):
        """Cancel all active encoding processes"""
        debug_print("Cancelling all encoding processes...")

        # Kill all ffmpeg processes system-wide (Windows)
        try:
            subprocess.run(['taskkill', '/F', '/IM', 'ffmpeg.exe'],
                         capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
            debug_print("Killed all ffmpeg processes system-wide")
        except Exception as e:
            debug_print(f"Error killing ffmpeg processes system-wide: {e}")

        self.cleanup_processes()

# Global encoder manager instance
encoder_manager = EncoderManager()
