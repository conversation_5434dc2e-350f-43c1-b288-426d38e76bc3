"""
VidMuncher GUI Module
Contains GUI components and event handlers
"""

import os
import threading
import tkinter as tk
from tkinter import ttk, filedialog
from PIL import Image, ImageTk

from config import *
from utils import (
    sanitize_filename, get_extension_from_preset, get_unique_filename,
    debug_print, validate_url, force_garbage_collection
)
from downloader import video_analyzer, video_downloader
from encoder import encoder_manager

class VidMuncherGUI:
    """Main GUI class for VidMuncher application"""
    
    def __init__(self):
        self.window = None
        self.video_data = {}
        self.thumbnail_imgtk = None
        self.is_downloading = False
        self.current_temp_files = []
        self.active_threads = []
        
        # GUI Components
        self.url_var = None
        self.url_entry = None
        self.video_info = None
        self.thumbnail_label = None
        self.preset_combo = None
        self.encoder_combo = None
        self.save_path_var = None
        self.save_entry = None
        self.analyze_button = None
        self.download_button = None
        self.cancel_button = None
        self.progress_canvas = None
        self.progress_fill = None
        self.progress_text_item = None
        
        self.setup_window()
        self.setup_gui_components()
        self.setup_styles()
    
    def setup_window(self):
        """Initialize main window"""
        self.window = tk.Tk()
        self.window.title(APP_TITLE)
        self.window.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.window.configure(bg=WINDOW_BG_COLOR)
        
        # Lock window - disable resize and maximize
        self.window.resizable(False, False)
        self.window.maxsize(WINDOW_WIDTH, WINDOW_HEIGHT)
        self.window.minsize(WINDOW_WIDTH, WINDOW_HEIGHT)

        # Apply dark title bar (Windows 10+ only)
        self.apply_dark_title_bar()

        self.setup_window_icon()

    def apply_dark_title_bar(self):
        """
        Applies a dark mode theme to the window title bar on Windows.
        This function only works on Windows operating systems.
        """
        try:
            import ctypes

            # Check for Windows 10 or later
            if ctypes.windll.kernel32.GetVersion() >= 0x0A000000:
                self.window.update()  # Ensure the window is fully created
                DWMWA_USE_IMMERSIVE_DARK_MODE = 20
                set_window_attribute = ctypes.windll.dwmapi.DwmSetWindowAttribute
                get_parent = ctypes.windll.user32.GetParent

                hwnd = get_parent(self.window.winfo_id())
                rendering_policy = DWMWA_USE_IMMERSIVE_DARK_MODE
                value = 2  # 2 for dark mode, 1 for light mode
                value = ctypes.c_int(value)
                set_window_attribute(hwnd, rendering_policy, ctypes.byref(value), ctypes.sizeof(value))
        except Exception as e:
            # Silently fail - dark title bar is not critical
            debug_print(f"Could not set dark title bar: {e}")

    def setup_window_icon(self):
        """Setup window icon"""
        icon_set = False
        
        # Try PNG icon first
        if ICON_PNG_PATH.exists():
            try:
                icon_image = Image.open(ICON_PNG_PATH)
                icon_photo = ImageTk.PhotoImage(icon_image)
                self.window.iconphoto(True, icon_photo)
                icon_set = True
                debug_print("Icon set from PNG")
            except Exception as e:
                debug_print(f"Failed to set PNG icon: {e}")
        
        # Fallback to ICO icon
        if not icon_set and ICON_PATH.exists():
            try:
                self.window.iconbitmap(ICON_PATH)
                icon_set = True
                debug_print("Icon set from ICO")
            except Exception as e:
                debug_print(f"Failed to set ICO icon: {e}")
        
        # Set app ID for taskbar
        if icon_set:
            try:
                import ctypes
                myappid = f'{APP_NAME.lower()}.app.1.0'
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
                debug_print("App ID set for taskbar")
            except:
                pass
    
    def setup_gui_components(self):
        """Setup all GUI components"""
        self.setup_header()
        self.setup_url_input()
        self.setup_video_info()
        self.setup_thumbnail()
        self.setup_preset_selection()
        self.setup_save_location()
        self.setup_buttons()
        self.setup_progress_bar()
    
    def setup_header(self):
        """Setup header section"""
        header_bg = tk.Frame(self.window, bg=HEADER_BG_COLOR, height=Layout.HEADER_HEIGHT)
        header_bg.place(x=0, y=0, width=WINDOW_WIDTH, height=Layout.HEADER_HEIGHT)
        
        if HEADER_PATH.exists():
            try:
                header_image = tk.PhotoImage(file=HEADER_PATH)
                tk.Label(self.window, image=header_image, bg=HEADER_BG_COLOR).place(
                    x=Layout.HEADER_IMAGE_X, y=Layout.HEADER_IMAGE_Y
                )
                # Keep reference to prevent garbage collection
                self.header_image = header_image
            except Exception as e:
                debug_print(f"Failed to load header image: {e}")
    
    def setup_url_input(self):
        """Setup URL input field"""
        self.url_var = tk.StringVar()
        self.url_entry = tk.Entry(
            self.window,
            textvariable=self.url_var,
            font=Fonts.DEFAULT,
            bg=HEADER_BG_COLOR,
            fg=PLACEHOLDER_COLOR,
            insertbackground=TEXT_COLOR,
            relief=tk.FLAT
        )
        self.url_entry.place(
            x=Layout.URL_ENTRY_X, y=Layout.URL_ENTRY_Y,
            width=Layout.URL_ENTRY_WIDTH, height=Layout.URL_ENTRY_HEIGHT
        )
        self.url_entry.insert(0, Layout.URL_PLACEHOLDER)
        
        # Bind events for placeholder behavior
        self.url_entry.bind("<FocusIn>", self.clear_url_placeholder)
        self.url_entry.bind("<FocusOut>", self.restore_url_placeholder)
    
    def setup_video_info(self):
        """Setup video information display"""
        # Create a frame to hold both placeholder text and video info
        self.video_info_frame = tk.Frame(self.window, bg=HEADER_BG_COLOR)
        self.video_info_frame.place(
            x=Layout.VIDEO_INFO_X, y=Layout.VIDEO_INFO_Y,
            width=Layout.VIDEO_INFO_WIDTH, height=Layout.VIDEO_INFO_HEIGHT
        )

        # Placeholder text using Label for precise center positioning
        self.video_info_placeholder = tk.Label(
            self.video_info_frame,
            text=Layout.VIDEO_INFO_PLACEHOLDER,
            bg=HEADER_BG_COLOR,
            fg=PLACEHOLDER_COLOR,
            font=Fonts.SMALL,
            anchor='center'
        )
        # Place at exact center of video info box
        self.video_info_placeholder.place(
            x=Layout.VIDEO_INFO_WIDTH // 2,
            y=Layout.VIDEO_INFO_HEIGHT // 2,
            anchor='center'
        )

        # Text widget for actual video info (will be shown when video is analyzed)
        self.video_info = tk.Text(
            self.video_info_frame,
            wrap=tk.WORD,
            bg=HEADER_BG_COLOR,
            fg=TEXT_COLOR,
            font=Fonts.SMALL,
            relief=tk.FLAT,
            state=tk.DISABLED,
            cursor="arrow"
        )
        # Initially hidden - will be placed when video info is loaded

        # Bind mousewheel events
        self.video_info.bind("<MouseWheel>", self.on_video_info_mousewheel)
        self.window.bind("<MouseWheel>", self.on_window_mousewheel)
    
    def setup_thumbnail(self):
        """Setup thumbnail display"""
        # Create a frame to hold both placeholder text and image
        self.thumbnail_frame = tk.Frame(self.window, bg=HEADER_BG_COLOR)
        self.thumbnail_frame.place(
            x=Layout.THUMBNAIL_X, y=Layout.THUMBNAIL_Y,
            width=Layout.THUMBNAIL_WIDTH, height=Layout.THUMBNAIL_HEIGHT
        )

        # Placeholder text using Label for precise center positioning
        self.thumbnail_placeholder = tk.Label(
            self.thumbnail_frame,
            text=Layout.THUMBNAIL_PLACEHOLDER,
            bg=HEADER_BG_COLOR,
            fg=PLACEHOLDER_COLOR,
            font=Fonts.SMALL,
            anchor='center'
        )
        # Place at exact center of thumbnail box
        self.thumbnail_placeholder.place(
            x=Layout.THUMBNAIL_WIDTH // 2,
            y=Layout.THUMBNAIL_HEIGHT // 2,
            anchor='center'
        )

        # Image label (will be shown when thumbnail is loaded)
        self.thumbnail_label = tk.Label(self.thumbnail_frame, bg=HEADER_BG_COLOR)
        # Initially hidden - will be placed when image is loaded
    
    def setup_preset_selection(self):
        """Setup preset selection components"""
        # Preset label
        tk.Label(
            self.window,
            text="Select Preset",
            font=Fonts.DEFAULT,
            bg=WINDOW_BG_COLOR,
            fg=TEXT_COLOR
        ).place(x=Layout.PRESET_LABEL_X, y=Layout.PRESET_LABEL_Y)
        
        # Preset combobox
        self.preset_combo = ttk.Combobox(
            self.window,
            state="readonly",
            font=Fonts.COMBO,
            style='Custom.TCombobox',
            values=DOWNLOAD_PRESETS
        )
        self.preset_combo.place(
            x=Layout.PRESET_COMBO_X, y=Layout.PRESET_COMBO_Y,
            width=Layout.PRESET_COMBO_WIDTH, height=Layout.PRESET_COMBO_HEIGHT
        )
        self.preset_combo.current(0)
        self.preset_combo.bind('<<ComboboxSelected>>', self.on_preset_change)

        # Re-encode label
        tk.Label(
            self.window,
            text="Re-encode",
            font=Fonts.DEFAULT,
            bg=WINDOW_BG_COLOR,
            fg=TEXT_COLOR
        ).place(x=Layout.REENCODE_LABEL_X, y=Layout.REENCODE_LABEL_Y)

        # Encoder combobox
        self.encoder_combo = ttk.Combobox(
            self.window,
            state="readonly",
            font=Fonts.COMBO,
            style='Custom.TCombobox',
            values=ENCODER_OPTIONS
        )
        self.encoder_combo.place(
            x=Layout.REENCODE_COMBO_X, y=Layout.REENCODE_COMBO_Y,
            width=Layout.REENCODE_COMBO_WIDTH, height=Layout.REENCODE_COMBO_HEIGHT
        )
        self.encoder_combo.current(0)  # Default to "Original Codec"
        self.encoder_combo.bind('<<ComboboxSelected>>', self.on_encoder_change)
    
    def setup_save_location(self):
        """Setup save location components"""
        # Save location label
        tk.Label(
            self.window,
            text="Save Location",
            font=Fonts.DEFAULT,
            bg=WINDOW_BG_COLOR,
            fg=TEXT_COLOR
        ).place(x=Layout.SAVE_LABEL_X, y=Layout.SAVE_LABEL_Y)
        
        # Save path entry
        self.save_path_var = tk.StringVar()
        self.save_entry = tk.Entry(
            self.window,
            textvariable=self.save_path_var,
            font=Fonts.SMALL,
            bg=HEADER_BG_COLOR,
            fg=TEXT_COLOR,
            relief=tk.FLAT,
            insertbackground=TEXT_COLOR
        )
        self.save_entry.place(
            x=Layout.SAVE_ENTRY_X, y=Layout.SAVE_ENTRY_Y,
            width=Layout.SAVE_ENTRY_WIDTH, height=Layout.SAVE_ENTRY_HEIGHT
        )
        
        # Browse button
        tk.Button(
            self.window,
            text="Browse",
            font=Fonts.BOLD,
            bg=BUTTON_COLOR,
            fg="white",
            relief=tk.FLAT,
            command=self.browse_save_path
        ).place(
            x=Layout.BROWSE_BUTTON_X, y=Layout.BROWSE_BUTTON_Y,
            width=Layout.BROWSE_BUTTON_WIDTH, height=Layout.BROWSE_BUTTON_HEIGHT
        )
    
    def setup_buttons(self):
        """Setup action buttons"""
        # Analyze button
        self.analyze_button = tk.Button(
            self.window,
            text="Analyze",
            font=Fonts.BOLD,
            bg=BUTTON_COLOR,
            fg="white",
            relief=tk.FLAT,
            command=self.analyze_video
        )
        self.analyze_button.place(
            x=Layout.ANALYZE_BUTTON_X, y=Layout.ANALYZE_BUTTON_Y,
            width=Layout.ANALYZE_BUTTON_WIDTH, height=Layout.ANALYZE_BUTTON_HEIGHT
        )
        
        # Download button
        self.download_button = tk.Button(
            self.window,
            text="Download",
            font=Fonts.BOLD,
            bg=BUTTON_COLOR,
            fg="white",
            relief=tk.FLAT,
            command=self.download_video,
            state=tk.DISABLED
        )
        self.download_button.place(
            x=Layout.DOWNLOAD_BUTTON_X, y=Layout.DOWNLOAD_BUTTON_Y,
            width=Layout.DOWNLOAD_BUTTON_WIDTH, height=Layout.DOWNLOAD_BUTTON_HEIGHT
        )
        
        # Cancel button (initially hidden)
        self.cancel_button = tk.Button(
            self.window,
            text="Cancel",
            font=Fonts.BOLD,
            bg="#89003E",
            fg="white",
            relief=tk.FLAT,
            command=self.cancel_download
        )
        # Don't place it initially - will be shown when needed
    
    def setup_progress_bar(self):
        """Setup progress bar"""
        progress_frame = tk.Frame(self.window, bg=HEADER_BG_COLOR)
        progress_frame.place(
            x=Layout.PROGRESS_X, y=Layout.PROGRESS_Y,
            width=Layout.PROGRESS_WIDTH, height=Layout.PROGRESS_HEIGHT
        )
        
        self.progress_canvas = tk.Canvas(
            progress_frame,
            bg=HEADER_BG_COLOR,
            highlightthickness=0,
            height=Layout.PROGRESS_HEIGHT
        )
        self.progress_canvas.place(relwidth=1, relheight=1)
        
        # Progress bar background and fill
        progress_bg = self.progress_canvas.create_rectangle(
            0, 0, Layout.PROGRESS_WIDTH, Layout.PROGRESS_HEIGHT,
            fill=HEADER_BG_COLOR, outline=HEADER_BG_COLOR
        )
        self.progress_fill = self.progress_canvas.create_rectangle(
            0, 0, 0, Layout.PROGRESS_HEIGHT,
            fill=BUTTON_COLOR, outline=BUTTON_COLOR
        )
        
        # Progress text
        self.progress_text_item = self.progress_canvas.create_text(
            Layout.PROGRESS_WIDTH // 2, Layout.PROGRESS_HEIGHT // 2,
            text="", font=Fonts.SMALL, fill=TEXT_COLOR, anchor=tk.CENTER
        )

    def setup_styles(self):
        """Setup TTK styles"""
        combo_style = ttk.Style()
        combo_style.theme_use('clam')

        # Configure combobox style
        combo_style.configure('Custom.TCombobox',
                             fieldbackground=HEADER_BG_COLOR,
                             background=BUTTON_COLOR,
                             foreground=TEXT_COLOR,
                             arrowcolor=TEXT_COLOR,
                             bordercolor=HEADER_BG_COLOR,
                             lightcolor=HEADER_BG_COLOR,
                             darkcolor=HEADER_BG_COLOR,
                             borderwidth=1,
                             relief='flat')

        # Configure combobox dropdown style
        combo_style.map('Custom.TCombobox',
                       fieldbackground=[('readonly', HEADER_BG_COLOR)],
                       selectbackground=[('readonly', HEADER_BG_COLOR)],
                       selectforeground=[('readonly', TEXT_COLOR)])

        # Configure dropdown listbox
        combo_style.configure('Custom.TCombobox.Listbox',
                             background=HEADER_BG_COLOR,
                             foreground=TEXT_COLOR,
                             selectbackground=BUTTON_COLOR,
                             selectforeground=TEXT_COLOR,
                             borderwidth=0)

        try:
            self.window.option_add('*TCombobox*Listbox.Background', HEADER_BG_COLOR)
            self.window.option_add('*TCombobox*Listbox.Foreground', TEXT_COLOR)
            self.window.option_add('*TCombobox*Listbox.selectBackground', BUTTON_COLOR)
            self.window.option_add('*TCombobox*Listbox.selectForeground', TEXT_COLOR)
            self.window.option_add('*TCombobox*Listbox.font', 'Poppins 9')
        except:
            pass

    # Event Handlers
    def clear_url_placeholder(self, event):
        """Clear URL placeholder on focus"""
        if self.url_entry.get() == Layout.URL_PLACEHOLDER:
            self.url_var.set("")
            self.url_entry.config(fg=TEXT_COLOR)

    def restore_url_placeholder(self, event):
        """Restore URL placeholder when empty"""
        if self.url_entry.get() == "":
            self.url_entry.insert(0, Layout.URL_PLACEHOLDER)
            self.url_entry.config(fg=PLACEHOLDER_COLOR)

    def on_video_info_mousewheel(self, event):
        """Handle mousewheel in video info area"""
        self.video_info.yview_scroll(int(-1*(event.delta/120)), "units")

    def on_window_mousewheel(self, event):
        """Handle mousewheel on window (for video info area)"""
        x, y = (self.window.winfo_pointerx() - self.window.winfo_rootx(),
                self.window.winfo_pointery() - self.window.winfo_rooty())
        if (Layout.VIDEO_INFO_X <= x <= Layout.VIDEO_INFO_X + Layout.VIDEO_INFO_WIDTH and
            Layout.VIDEO_INFO_Y <= y <= Layout.VIDEO_INFO_Y + Layout.VIDEO_INFO_HEIGHT):
            self.video_info.yview_scroll(int(-1*(event.delta/120)), "units")

    def on_preset_change(self, event):
        """Handle preset selection change"""
        selected = self.preset_combo.get()
        debug_print(f"Preset changed to: {selected}")

        # Disable encoder for audio presets
        if "Audio" in selected:
            self.encoder_combo.current(0)  # Set to "Original Codec"
            self.encoder_combo.config(state="disabled")
        else:
            self.encoder_combo.config(state="readonly")

        # Update preview path if video data exists
        if 'title' in self.video_data:
            self.update_preview_path()

    def on_encoder_change(self, event):
        """Handle encoder selection change"""
        selected = self.encoder_combo.get()
        debug_print(f"Encoder changed to: {selected}")

        # Update preview path if video data exists
        if 'title' in self.video_data:
            self.update_preview_path()

    def is_encoding_enabled(self):
        """Check if encoding is enabled based on encoder selection"""
        return self.encoder_combo.get() != "Original Codec"

    # Utility Methods




    def clear_thumbnail(self):
        """Clear previous thumbnail to prevent memory leaks"""
        if self.thumbnail_imgtk:
            self.thumbnail_imgtk = None
            force_garbage_collection()

    def reset_video_data(self):
        """Reset video data and clear thumbnail"""
        self.video_data.clear()
        self.clear_thumbnail()

        # Show video info placeholder and hide video info
        self.video_info.place_forget()
        self.video_info_placeholder.place(
            x=Layout.VIDEO_INFO_WIDTH // 2,
            y=Layout.VIDEO_INFO_HEIGHT // 2,
            anchor='center'
        )

        # Show thumbnail placeholder and hide image
        self.thumbnail_label.place_forget()
        self.thumbnail_placeholder.place(
            x=Layout.THUMBNAIL_WIDTH // 2,
            y=Layout.THUMBNAIL_HEIGHT // 2,
            anchor='center'
        )
        self.thumbnail_label.config(image="")
        self.thumbnail_label.config(bg=HEADER_BG_COLOR)

    def update_preview_path(self):
        """Update preview path based on preset and H.264 status"""
        if 'title' not in self.video_data:
            return

        title = self.video_data['title']
        safe_title = sanitize_filename(title)

        encoding_enabled = self.is_encoding_enabled()
        encoder_selection = self.encoder_combo.get()
        ext = get_extension_from_preset(self.preset_combo.get(), encoding_enabled, encoder_selection)

        filename_with_ext = f"{safe_title}.{ext}"
        full_path_with_ext = os.path.join(DEFAULT_DOWNLOAD_PATH, filename_with_ext)
        unique_full_path = get_unique_filename(full_path_with_ext)
        unique_name_without_ext = os.path.splitext(os.path.basename(unique_full_path))[0]
        unique_preview_path = os.path.join(DEFAULT_DOWNLOAD_PATH, unique_name_without_ext)

        self.save_path_var.set(unique_preview_path)
        debug_print(f"Updated preview path: {unique_preview_path} (will be {ext})")

    def set_button_states(self, analyze_enabled=True, download_enabled=False):
        """Set state of Analyze and Download buttons"""
        if analyze_enabled:
            self.analyze_button.config(state=tk.NORMAL, bg=BUTTON_COLOR)
        else:
            self.analyze_button.config(state=tk.DISABLED, bg=BUTTON_DISABLED_COLOR)

        if download_enabled:
            self.download_button.config(state=tk.NORMAL, bg=BUTTON_COLOR)
        else:
            self.download_button.config(state=tk.DISABLED, bg=BUTTON_DISABLED_COLOR)

    def show_cancel_button(self, show=True):
        """Show or hide cancel button"""
        if show:
            self.cancel_button.place(
                x=Layout.CANCEL_BUTTON_X, y=Layout.CANCEL_BUTTON_Y,
                width=Layout.CANCEL_BUTTON_WIDTH, height=Layout.CANCEL_BUTTON_HEIGHT
            )
        else:
            self.cancel_button.place_forget()

    def update_progress(self, text, progress=None, error=False):
        """Update progress bar and text"""
        color = "#FF5050" if error else TEXT_COLOR
        self.progress_canvas.itemconfig(self.progress_text_item, text=text, fill=color)

        if progress is not None:
            width = int((progress / 100) * Layout.PROGRESS_WIDTH)
            self.progress_canvas.coords(self.progress_fill, 0, 0, width, Layout.PROGRESS_HEIGHT)

        self.window.update_idletasks()

    # Main Action Methods
    def browse_save_path(self):
        """Browse for save location"""
        encoding_enabled = self.is_encoding_enabled()
        encoder_selection = self.encoder_combo.get()
        extension = get_extension_from_preset(self.preset_combo.get(), encoding_enabled, encoder_selection)

        # Default filename without extension for preview
        current_path = self.save_path_var.get()
        default_name = os.path.basename(current_path) if current_path else "video"

        path = filedialog.asksaveasfilename(
            defaultextension=f".{extension}",
            initialfile=f"{default_name}.{extension}",
            filetypes=[("Media Files", f"*.{extension}")]
        )
        if path:
            # Save path without extension for GUI preview
            path_without_ext = os.path.splitext(path)[0]
            self.save_path_var.set(path_without_ext)

    def analyze_video(self):
        """Analyze video URL"""
        url = self.url_var.get().strip()
        if not url or url == Layout.URL_PLACEHOLDER:
            self.update_progress(Messages.URL_EMPTY, error=True)
            return

        if not validate_url(url):
            self.update_progress(Messages.INVALID_URL, error=True)
            return

        # Disable buttons during analyze
        self.set_button_states(analyze_enabled=False, download_enabled=False)

        def analyze_task():
            success, video_data, error_msg = video_analyzer.analyze_video(
                url, progress_callback=self.update_progress
            )

            if success and video_data:
                self.video_data.clear()
                self.video_data.update(video_data)

                # Update video info display
                title = video_data.get("title", "N/A")
                desc = video_data.get("description", "")

                # Hide placeholder and show video info
                self.video_info_placeholder.place_forget()
                self.video_info.place(x=0, y=0, width=Layout.VIDEO_INFO_WIDTH, height=Layout.VIDEO_INFO_HEIGHT)

                self.video_info.config(state=tk.NORMAL)
                self.video_info.delete("1.0", tk.END)
                self.video_info.insert(tk.END, f"{title}\n\n{desc}")
                self.video_info.config(fg=TEXT_COLOR)
                self.video_info.config(state=tk.DISABLED)

                # Download thumbnail asynchronously
                thumbnail_url = video_data.get("thumbnail", "")
                if thumbnail_url:
                    self.download_thumbnail_async(thumbnail_url)

                # Set default save path
                self.update_preview_path()

                self.update_progress(Messages.READY_DOWNLOAD, progress=0)
                self.set_button_states(analyze_enabled=True, download_enabled=True)
            else:
                self.update_progress(error_msg or Messages.FAILED_VIDEO_INFO, error=True)
                self.reset_video_data()
                self.set_button_states(analyze_enabled=True, download_enabled=False)

        # Create daemon thread for auto-cleanup
        thread = threading.Thread(target=analyze_task, daemon=True, name="AnalyzeThread")
        self.active_threads.append(thread)
        thread.start()

    def download_thumbnail_async(self, thumbnail_url):
        """Download thumbnail asynchronously"""
        def download_task():
            thumbnail_img = video_analyzer.download_thumbnail(
                thumbnail_url,
                size=(Layout.THUMBNAIL_WIDTH, Layout.THUMBNAIL_HEIGHT)
            )

            def update_thumbnail():
                if thumbnail_img:
                    self.clear_thumbnail()
                    self.thumbnail_imgtk = thumbnail_img

                    # Hide placeholder and show image
                    self.thumbnail_placeholder.place_forget()
                    self.thumbnail_label.place(x=0, y=0, width=Layout.THUMBNAIL_WIDTH, height=Layout.THUMBNAIL_HEIGHT)
                    self.thumbnail_label.config(image=self.thumbnail_imgtk)
                else:
                    # Show placeholder and hide image
                    self.thumbnail_label.place_forget()
                    self.thumbnail_placeholder.place(
                        x=Layout.THUMBNAIL_WIDTH // 2,
                        y=Layout.THUMBNAIL_HEIGHT // 2,
                        anchor='center'
                    )
                    self.thumbnail_label.config(image="")

            self.window.after(0, update_thumbnail)

        threading.Thread(target=download_task, daemon=True, name="ThumbnailThread").start()

    def download_video(self):
        """Download video"""
        url = self.url_var.get().strip()
        out_path = self.save_path_var.get()
        preset = self.preset_combo.get()

        if not url or url == Layout.URL_PLACEHOLDER:
            self.update_progress(Messages.URL_EMPTY, error=True)
            return

        # Disable buttons and show cancel button
        self.set_button_states(analyze_enabled=False, download_enabled=False)
        self.show_cancel_button(True)

        # Set download state
        self.is_downloading = True
        self.current_temp_files.clear()

        def download_task():
            encoding_enabled = self.is_encoding_enabled()

            # Download video
            success, final_path, error_msg = video_downloader.download_video(
                url, out_path, preset, encoding_enabled,
                progress_callback=self.update_progress,
                cancel_check=lambda: not self.is_downloading
            )

            if success and final_path:
                debug_print(f"🔍 Download success check:")
                debug_print(f"  - Preset: {preset}")
                debug_print(f"  - Audio in preset: {'Audio' in preset}")
                debug_print(f"  - Encoding enabled: {encoding_enabled}")
                debug_print(f"  - Selected encoder: {self.encoder_combo.get()}")
                debug_print(f"  - Not Original Codec: {self.encoder_combo.get() != 'Original Codec'}")

                if "Audio" not in preset and encoding_enabled and self.encoder_combo.get() != "Original Codec":
                    debug_print("✅ Starting encoding process...")
                    # Need encoding
                    self.encode_video_h264_inline(final_path)
                else:
                    debug_print("⏭️ Skipping encoding, completing download...")
                    # Download complete - update timestamp for visibility
                    self.update_file_timestamp(final_path, preset)
                    self.complete_download(success=True, message=Messages.DOWNLOAD_COMPLETE)
            else:
                if self.is_downloading:  # Only show error if not cancelled
                    # Use more specific error messages
                    if error_msg and "cancelled" in error_msg.lower():
                        self.update_progress(Messages.DOWNLOAD_CANCELLED, error=True)
                    elif error_msg and "interrupted" in error_msg.lower():
                        self.update_progress(Messages.DOWNLOAD_INTERRUPTED, error=True)
                    else:
                        self.update_progress(error_msg or Messages.DOWNLOAD_FAILED, error=True)
                    self.complete_download(success=False)

        # Create download thread
        thread = threading.Thread(target=download_task, daemon=True, name="DownloadThread")
        self.active_threads.append(thread)
        thread.start()

    def encode_video_h264_inline(self, input_file):
        """Encode video using selected encoder"""
        try:
            # Get selected encoder
            selected_encoder = self.encoder_combo.get()
            debug_print(f"🎯 Selected encoder: {selected_encoder}")

            encoder_config = encoder_manager.get_encoder_config(selected_encoder)
            debug_print(f"🔧 Encoder config: {encoder_config}")

            if not encoder_config:
                self.update_progress("Invalid encoder selection", error=True)
                self.complete_download(success=False)
                return

            self.update_progress(f"Preparing {encoder_config['name']} encoding...", progress=0)

            # Find actual file
            from utils import find_downloaded_file
            actual_file = find_downloaded_file(os.path.splitext(input_file)[0])

            if not actual_file or not os.path.exists(actual_file):
                if self.is_downloading:
                    self.update_progress(Messages.FILE_NOT_FOUND, error=True)
                    self.complete_download(success=False)
                return

            debug_print(f"Found source file: {actual_file}")

            # Setup paths
            base_path = os.path.splitext(input_file)[0]
            final_output = f"{base_path}.mp4"
            temp_path = f"{base_path}_temp.mp4"

            # Track temp file
            self.current_temp_files.append(temp_path)

            # Test encoder availability
            if not encoder_manager.test_encoder_availability(encoder_config):
                # Fallback to CPU encoder
                debug_print(f"⚠️ {encoder_config['name']} not available, falling back to CPU...")
                encoder_config = encoder_manager.get_encoder_config("H.264 (CPU)")
                if not encoder_manager.test_encoder_availability(encoder_config):
                    self.update_progress("No suitable encoder available", error=True)
                    self.complete_download(success=False)
                    return

            # Get progress message from mapping
            from config import ENCODER_PROGRESS_MESSAGES
            progress_message = ENCODER_PROGRESS_MESSAGES.get(selected_encoder, f"Encoding to {encoder_config['name']}")

            self.update_progress(f"{progress_message} - 0%", progress=0)

            # Build FFmpeg command using new method
            ffmpeg_cmd = encoder_manager.build_encoding_command_v2(actual_file, temp_path, encoder_config)

            debug_print(f"🚀 FFmpeg command: {' '.join(ffmpeg_cmd)}")

            # Run FFmpeg with real-time progress monitoring
            import subprocess
            ffmpeg_process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            # Real-time progress monitoring
            duration_seconds = None
            encoding_progress = 0

            for line in ffmpeg_process.stdout:
                # Check for cancellation
                if not self.is_downloading:
                    debug_print("FFmpeg encoding cancelled by user")
                    break

                line = line.strip()
                debug_print(f"FFmpeg: {line}")

                # Extract duration
                if "Duration:" in line and not duration_seconds:
                    duration_seconds = encoder_manager._extract_duration(line)
                    if duration_seconds:
                        debug_print(f"Video duration: {duration_seconds} seconds")

                # Extract progress
                elif "time=" in line and duration_seconds:
                    progress = encoder_manager._extract_progress(line, duration_seconds)
                    if progress is not None and progress > encoding_progress:
                        encoding_progress = progress
                        self.update_progress(f"{progress_message} - {progress:.1f}%", progress)

                # Fallback progress
                elif encoding_progress == 0 and any(keyword in line.lower() for keyword in ['frame=', 'fps=', 'bitrate=']):
                    encoding_progress = min(50, encoding_progress + 10)
                    self.update_progress(f"{progress_message} - {encoding_progress}%", encoding_progress)

            # Wait for process completion
            ffmpeg_process.wait()

            debug_print(f"FFmpeg process completed with return code: {ffmpeg_process.returncode}")

            if ffmpeg_process.returncode == 0:
                # Encoding successful
                self.update_progress(f"Finalizing {selected_encoder}...", progress=99)

                try:
                    if os.path.exists(temp_path):
                        # Remove original file if different from final output
                        if actual_file != final_output and os.path.exists(actual_file):
                            os.remove(actual_file)
                            debug_print(f"Removed source file: {actual_file}")

                        # Move encoded file to final location
                        import shutil
                        shutil.move(temp_path, final_output)
                        debug_print(f"Moved encoded file to: {final_output}")

                        self.complete_download(success=True, message=Messages.DOWNLOAD_COMPLETE)
                    else:
                        # Check if this is a cancellation
                        if not self.is_downloading:
                            debug_print("Encoding output check cancelled by user")
                            return  # Don't show error, cancel function will handle UI
                        else:
                            self.update_progress(Messages.ENCODING_ERROR, error=True)
                            debug_print(f"Expected output file not found: {temp_path}")
                            self.complete_download(success=False)

                except Exception as e:
                    # Check if this is a cancellation
                    if not self.is_downloading:
                        debug_print("File move cancelled by user")
                        return  # Don't show error, cancel function will handle UI
                    else:
                        self.update_progress(Messages.FILE_MOVE_ERROR, error=True)
                        debug_print(f"Move error: {str(e)}")
                        self.complete_download(success=False)
            else:
                # Check if this is a cancellation
                if not self.is_downloading:
                    # This was a cancellation, not an encoding failure
                    debug_print("H264 encoding was cancelled by user")
                    return  # Don't show error, cancel function will handle UI
                else:
                    # NOT a cancellation - show error
                    self.update_progress(Messages.ENCODING_FAILED, error=True)
                    debug_print(f"FFmpeg failed with return code: {ffmpeg_process.returncode}")
                    self.complete_download(success=False)

            # Clean up temp file
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass

        except Exception as e:
            # Check if this is a cancellation
            if not self.is_downloading:
                debug_print("Encoding exception due to cancellation")
                return  # Don't show error, cancel function will handle UI
            else:
                self.update_progress("Encoding Error!", error=True)
                debug_print(f"Encoding exception: {str(e)}")
                self.complete_download(success=False)

    def update_file_timestamp(self, file_path, preset):
        """Update file timestamp for visibility in explorer"""
        try:
            if "Audio" in preset:
                # Audio preset - update file timestamp
                self.update_audio_timestamp(file_path)
            else:
                # Video preset without H.264 - update video timestamp
                self.update_video_timestamp(file_path)

        except Exception as e:
            debug_print(f"Error updating file timestamp: {str(e)}")

    def update_video_timestamp(self, file_path):
        """Update video file timestamp"""
        try:
            import time
            import glob

            # Find actual video file
            video_file = None

            # Determine base path based on extension
            encoding_enabled = self.is_encoding_enabled()
            encoder_selection = self.encoder_combo.get()
            extension = get_extension_from_preset(self.preset_combo.get(), encoding_enabled, encoder_selection)

            if extension == "%(ext)s":
                # File without predetermined extension
                base_path = file_path
            else:
                # File with predetermined extension
                base_path = file_path.replace(f".{extension}", "")

            # Possible video extensions
            video_extensions = [".webm", ".mp4", ".mkv", ".m4v", ".avi"]

            # Method 1: Search for file with different extensions
            for ext in video_extensions:
                test_path = base_path + ext
                if os.path.exists(test_path):
                    video_file = test_path
                    debug_print(f"Found video file (direct): {video_file}")
                    break

            # Method 2: If not found, try glob search
            if not video_file:
                safe_base = base_path.replace('[', r'\[').replace(']', r'\]')
                pattern = safe_base + ".*"
                matches = glob.glob(pattern)
                debug_print(f"Glob pattern: {pattern}")
                debug_print(f"Glob matches: {matches}")

                if matches:
                    for match in matches:
                        if any(match.lower().endswith(ext) for ext in video_extensions):
                            video_file = match
                            debug_print(f"Found video file (glob): {video_file}")
                            break

            # Method 3: Search based on filename containing base name
            if not video_file:
                try:
                    directory = os.path.dirname(base_path)
                    base_name = os.path.basename(base_path)

                    if os.path.exists(directory):
                        all_files = os.listdir(directory)
                        debug_print(f"Searching in directory: {directory}")
                        debug_print(f"Base name: {base_name}")

                        for file in all_files:
                            if base_name.lower() in file.lower():
                                if any(file.lower().endswith(ext) for ext in video_extensions):
                                    video_file = os.path.join(directory, file)
                                    debug_print(f"Found video file (directory scan): {video_file}")
                                    break
                except Exception as scan_e:
                    debug_print(f"Directory scan error: {scan_e}")

            if video_file and os.path.exists(video_file):
                # Update timestamp to current time
                current_time = time.time()
                os.utime(video_file, (current_time, current_time))

                debug_print(f"✅ Successfully updated timestamp for video file: {video_file}")
                # Verify timestamp update
                stat_info = os.stat(video_file)
                debug_print(f"New modification time: {time.ctime(stat_info.st_mtime)}")
            else:
                debug_print(f"❌ Video file not found for timestamp update")
                debug_print(f"Expected base: {base_path}")
                debug_print(f"Checked extensions: {video_extensions}")

        except Exception as e:
            debug_print(f"Error updating video timestamp: {str(e)}")

    def update_audio_timestamp(self, file_path):
        """Update audio file timestamp"""
        try:
            import time

            # Find actual audio file
            audio_file = None

            # Get directory from output path
            directory = os.path.dirname(file_path)
            base_name = os.path.basename(file_path)

            # Remove extension from base name for search
            if "." in base_name:
                base_name = os.path.splitext(base_name)[0]

            # Possible audio extensions
            audio_extensions = [".mp3", ".wav", ".m4a", ".aac", ".ogg"]

            # Method 1: Direct extension check
            for ext in audio_extensions:
                test_path = os.path.join(directory, base_name + ext)
                if os.path.exists(test_path):
                    audio_file = test_path
                    debug_print(f"Found audio file (direct): {audio_file}")
                    break

            # Method 2: Directory scan for similar names
            if not audio_file:
                try:
                    if os.path.exists(directory):
                        all_files = os.listdir(directory)
                        debug_print(f"Searching for audio in directory: {directory}")
                        debug_print(f"Base name: {base_name}")

                        for file in all_files:
                            if base_name.lower() in file.lower():
                                if any(file.lower().endswith(ext) for ext in audio_extensions):
                                    audio_file = os.path.join(directory, file)
                                    debug_print(f"Found audio file (directory scan): {audio_file}")
                                    break
                except Exception as e:
                    debug_print(f"Directory listing failed: {e}")

            if audio_file and os.path.exists(audio_file):
                # Update timestamp to current time
                current_time = time.time()
                os.utime(audio_file, (current_time, current_time))

                debug_print(f"✅ Successfully updated timestamp for audio file: {audio_file}")
                # Verify timestamp update
                stat_info = os.stat(audio_file)
                debug_print(f"New modification time: {time.ctime(stat_info.st_mtime)}")
            else:
                debug_print(f"❌ Audio file not found for timestamp update")
                debug_print(f"Expected base: {base_name}")
                debug_print(f"Checked extensions: {audio_extensions}")

        except Exception as e:
            debug_print(f"Error updating audio timestamp: {str(e)}")

    def encode_video_h264(self, input_file):
        """Encode video to H.264"""
        try:
            # Determine output path
            base_path = os.path.splitext(input_file)[0]
            temp_path = base_path + "_temp.mp4"
            final_path = base_path + ".mp4"

            # Track temp file
            self.current_temp_files.append(temp_path)

            # Get selected encoder
            selected_encoder = self.encoder_combo.get()

            # Start encoding
            success, error_msg = encoder_manager.encode_video(
                input_file, temp_path,
                encoder_selection=selected_encoder,
                progress_callback=self.update_progress,
                cancel_check=lambda: not self.is_downloading
            )

            if success:
                # Finalize encoding
                self.update_progress("Finalizing H264...", progress=99)

                try:
                    # Remove original file if different from final output
                    if input_file != final_path and os.path.exists(input_file):
                        os.remove(input_file)
                        debug_print(f"Removed source file: {input_file}")

                    # Move encoded file to final location
                    if os.path.exists(temp_path):
                        os.rename(temp_path, final_path)
                        debug_print(f"Moved encoded file to: {final_path}")

                        self.complete_download(success=True, message=Messages.DOWNLOAD_COMPLETE)
                    else:
                        if self.is_downloading:
                            self.update_progress(Messages.ENCODING_ERROR, error=True)
                            self.complete_download(success=False)

                except Exception as e:
                    if self.is_downloading:
                        self.update_progress(Messages.FILE_MOVE_ERROR, error=True)
                        debug_print(f"Move error: {str(e)}")
                        self.complete_download(success=False)
            else:
                # Check if this is a cancellation
                if not self.is_downloading:
                    # This was a cancellation, not an encoding failure
                    debug_print("H264 encoding was cancelled by user")
                    return  # Don't show error, cancel function will handle UI
                else:
                    # NOT a cancellation - show error
                    self.update_progress(Messages.ENCODING_FAILED, error=True)
                    debug_print(f"FFmpeg failed: {error_msg}")
                    self.complete_download(success=False)

            # Clean up temp files
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass

        except Exception as e:
            # Check if this is a cancellation
            if not self.is_downloading:
                debug_print("Encoding exception due to cancellation")
                return  # Don't show error, cancel function will handle UI
            else:
                self.update_progress("Encoding Error!", error=True)
                debug_print(f"Encoding exception: {str(e)}")
                self.complete_download(success=False)

    def cancel_download(self):
        """Cancel current download/encoding"""
        self.is_downloading = False

        # Cancel download
        video_downloader.cancel_download()

        # Cancel encoding
        encoder_manager.cancel_encoding()

        # Clean up temp files
        from utils import cleanup_temp_files
        cleanup_temp_files(self.current_temp_files)

        self.update_progress(Messages.DOWNLOAD_CANCELLED, error=True)
        self.complete_download(success=False)

    def complete_download(self, success=True, message=None):
        """Complete download process and reset UI"""
        self.is_downloading = False

        if message:
            self.update_progress(message, progress=100 if success else None, error=not success)

        # Reset UI
        self.show_cancel_button(False)
        self.set_button_states(analyze_enabled=True, download_enabled=True)

        # Clean up
        self.cleanup_finished_processes()

    def cleanup_finished_processes(self):
        """Remove finished processes and dead threads from tracking lists"""
        # Remove dead threads
        self.active_threads[:] = [t for t in self.active_threads if t.is_alive()]

        # Clean up downloader and encoder processes
        video_downloader.cleanup_processes()
        encoder_manager.cleanup_processes()

    def run(self):
        """Start the GUI main loop"""
        try:
            self.window.mainloop()
        except KeyboardInterrupt:
            self.cleanup_and_exit()

    def cleanup_and_exit(self):
        """Clean up resources and exit"""
        self.is_downloading = False

        # Cancel any active operations
        video_downloader.cancel_download()
        encoder_manager.cancel_encoding()

        # Clean up temp files
        from utils import cleanup_temp_files
        cleanup_temp_files(self.current_temp_files)

        # Clean up processes
        self.cleanup_finished_processes()

        # Destroy window
        if self.window:
            self.window.destroy()

# Create global GUI instance
gui = VidMuncherGUI()
