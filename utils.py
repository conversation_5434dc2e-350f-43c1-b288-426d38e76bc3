"""
VidMuncher Utility Functions Module
Contains file operations, filename sanitization, and helper functions
"""

import os
import time
import datetime
import glob
import gc
from config import (
    DEBUG_MODE, 
    INVALID_FILENAME_CHARS, 
    MAX_FILENAME_LENGTH, 
    MAX_UNIQUE_FILENAME_ATTEMPTS,
    VIDEO_EXTENSIONS
)

def sanitize_filename(filename):
    """Sanitize filename by removing invalid characters and limiting length"""
    safe_filename = filename

    for char in INVALID_FILENAME_CHARS:
        safe_filename = safe_filename.replace(char, '_')

    if len(safe_filename) > MAX_FILENAME_LENGTH:
        safe_filename = safe_filename[:MAX_FILENAME_LENGTH]

    return safe_filename

def get_extension_from_preset(preset, encoding_enabled=True, encoder_selection="H.264 (CPU)"):
    """Get file extension based on selected preset and encoder"""
    if "Audio" in preset:
        if "wav" in preset:
            return "wav"
        else:
            return "mp3"
    else:
        if encoding_enabled and encoder_selection != "Original Codec":
            # All encoders output MP4 container
            return "mp4"
        else:
            # Keep original extension
            return "%(ext)s"

def get_unique_filename(filepath):
    """
    Generate unique filename if file exists by adding (1), (2), etc
    
    Args:
        filepath (str): Original file path
        
    Returns:
        str: Unique file path
    """
    if not os.path.exists(filepath):
        return filepath

    # Split path into directory, name, and extension
    directory = os.path.dirname(filepath)
    filename = os.path.basename(filepath)
    name, ext = os.path.splitext(filename)

    counter = 1
    while True:
        # Generate new filename with counter
        new_name = f"{name} ({counter}){ext}"
        new_path = os.path.join(directory, new_name)

        if not os.path.exists(new_path):
            if DEBUG_MODE:
                print(f"Generated unique filename: {new_path}")
            return new_path

        counter += 1

        # Safety limit to prevent infinite loop
        if counter > MAX_UNIQUE_FILENAME_ATTEMPTS:
            if DEBUG_MODE:
                print(f"Warning: Reached counter limit for {filepath}")
            break

    # Fallback with timestamp if counter limit reached
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    fallback_name = f"{name}_{timestamp}{ext}"
    fallback_path = os.path.join(directory, fallback_name)

    if DEBUG_MODE:
        print(f"Using timestamp fallback: {fallback_path}")

    return fallback_path

def get_unique_filename_without_ext(base_path):
    """
    Generate unique filename without extension, checking multiple possible extensions
    
    Args:
        base_path (str): Base path without extension
        
    Returns:
        str: Unique base path without extension
    """
    counter = 0
    while True:
        if counter == 0:
            test_base = base_path
        else:
            directory = os.path.dirname(base_path)
            filename = os.path.basename(base_path)
            test_base = os.path.join(directory, f"{filename} ({counter})")

        # Check if any file with possible video extensions exists
        file_exists = False
        for ext in VIDEO_EXTENSIONS:
            if os.path.exists(f"{test_base}{ext}"):
                file_exists = True
                break

        if not file_exists:
            return test_base

        counter += 1

def find_downloaded_file(base_path, possible_extensions=None):
    """
    Find the actual downloaded file by checking multiple possible extensions
    
    Args:
        base_path (str): Base path without extension
        possible_extensions (list): List of extensions to check
        
    Returns:
        str or None: Path to found file, or None if not found
    """
    if possible_extensions is None:
        possible_extensions = VIDEO_EXTENSIONS

    # First try exact matches
    for ext in possible_extensions:
        test_path = base_path + ext
        if os.path.exists(test_path):
            return test_path

    # If not found, try glob pattern for similar files
    try:
        # Escape special characters for glob
        safe_base = base_path.replace('[', r'\[').replace(']', r'\]')
        pattern = safe_base + ".*"
        matches = glob.glob(pattern)
        if matches:
            # Return first match
            return matches[0]
    except Exception as e:
        if DEBUG_MODE:
            print(f"Glob search error: {e}")

    return None

def cleanup_file_with_timeout(filepath, max_attempts=3, total_timeout=2):
    """
    Optimized file cleanup with timeout
    
    Args:
        filepath (str): Path to file to cleanup
        max_attempts (int): Maximum cleanup attempts
        total_timeout (float): Total timeout in seconds
        
    Returns:
        bool: True if cleanup successful, False otherwise
    """
    start_time = time.time()
    for attempt in range(max_attempts):
        if time.time() - start_time > total_timeout:
            break  # Don't exceed total timeout
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
        except PermissionError:
            time.sleep(0.1)  # Short sleep
        except Exception:
            break  # Don't retry on other errors
    return False

def cleanup_temp_files(temp_files_list):
    """
    Clean up a list of temporary files
    
    Args:
        temp_files_list (list): List of temporary file paths
    """
    for temp_file in temp_files_list[:]:  # Create copy to avoid modification during iteration
        if cleanup_file_with_timeout(temp_file):
            temp_files_list.remove(temp_file)
            if DEBUG_MODE:
                print(f"Cleaned up temp file: {temp_file}")
        else:
            if DEBUG_MODE:
                print(f"Failed to clean up temp file: {temp_file}")

def force_garbage_collection():
    """Force garbage collection to free memory"""
    gc.collect()

def debug_print(message):
    """Print debug message if debug mode is enabled"""
    if DEBUG_MODE:
        print(f"[DEBUG] {message}")

def format_file_size(size_bytes):
    """
    Format file size in human readable format
    
    Args:
        size_bytes (int): Size in bytes
        
    Returns:
        str: Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def validate_url(url):
    """
    Basic URL validation
    
    Args:
        url (str): URL to validate
        
    Returns:
        bool: True if URL appears valid, False otherwise
    """
    if not url or url.strip() == "":
        return False
    
    url = url.strip()
    
    # Check for common URL patterns
    if url.startswith(('http://', 'https://', 'www.')):
        return True
    
    # Check for common video platforms
    video_platforms = ['youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com']
    for platform in video_platforms:
        if platform in url.lower():
            return True
    
    return False

def get_safe_path_preview(full_path, max_length=50):
    """
    Get a safe preview of a file path for display
    
    Args:
        full_path (str): Full file path
        max_length (int): Maximum length for preview
        
    Returns:
        str: Truncated path preview
    """
    if len(full_path) <= max_length:
        return full_path
    
    # Try to keep the filename and truncate the directory part
    filename = os.path.basename(full_path)
    if len(filename) < max_length - 10:  # Leave room for ".../"
        directory = os.path.dirname(full_path)
        available_length = max_length - len(filename) - 4  # 4 for ".../"
        if available_length > 0:
            truncated_dir = directory[:available_length]
            return f"{truncated_dir}...//{filename}"
    
    # If filename is too long, truncate it
    return full_path[:max_length-3] + "..."
