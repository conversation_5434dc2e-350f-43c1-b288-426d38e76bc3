#!/usr/bin/env python3
"""
VidMuncher - Video Downloader Application
A modular video downloading application using yt-dlp and ffmpeg

This is the main entry point that orchestrates all the modular components.
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modular components
from config import DEBUG_MODE, APP_NAME, APP_VERSION
from utils import debug_print
from gui import gui

def main():
    """Main entry point for VidMuncher application"""
    try:
        debug_print(f"Starting {APP_NAME} {APP_VERSION}")
        debug_print(f"Debug mode: {DEBUG_MODE}")
        
        # Check if required dependencies exist
        check_dependencies()
        
        # Start the GUI
        debug_print("Launching GUI...")
        gui.run()
        
    except KeyboardInterrupt:
        debug_print("Application interrupted by user")
        gui.cleanup_and_exit()
    except Exception as e:
        debug_print(f"Fatal error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def check_dependencies():
    """Check if required files and dependencies exist"""
    from config import YTDLP_PATH, FFMPEG_PATH, FFPROBE_PATH, BIN_PATH, HEADER_PATH

    # Check if bin directory exists
    if not BIN_PATH.exists():
        raise FileNotFoundError(f"Binary directory not found: {BIN_PATH}")

    # Check if yt-dlp exists
    if not YTDLP_PATH.exists():
        raise FileNotFoundError(f"yt-dlp not found: {YTDLP_PATH}")

    # Check if ffmpeg exists
    if not FFMPEG_PATH.exists():
        raise FileNotFoundError(f"ffmpeg not found: {FFMPEG_PATH}")

    # Check if ffprobe exists (for AV1 codec detection)
    if not FFPROBE_PATH.exists():
        debug_print(f"Warning: ffprobe not found: {FFPROBE_PATH}")
        debug_print("AV1 codec detection will be limited without ffprobe")

    # Check if header asset exists (bundled or external)
    if not HEADER_PATH.exists():
        debug_print(f"Warning: Header asset not found: {HEADER_PATH}")

    debug_print("All dependencies checked successfully")

if __name__ == "__main__":
    main()
