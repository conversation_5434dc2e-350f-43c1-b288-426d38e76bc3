from pathlib import Path
import os
import sys
import getpass
import subprocess
import threading
import json
import requests
import shutil
import re
import time
import glob
from io import BytesIO
from tkinter import *
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk

# Path and Constants - Support for executable
if getattr(sys, 'frozen', False):
    # Running as executable (PyInstaller)
    ROOT = Path(sys.executable).parent
else:
    # Running as Python script
    ROOT = Path(__file__).parent
BIN_PATH = ROOT / "bin"
ASSETS_PATH = ROOT / "assets"
YTDLP_PATH = BIN_PATH / "yt-dlp.exe"
FFMPEG_PATH = BIN_PATH / "ffmpeg.exe"
ICON_PATH = ASSETS_PATH / "icon.ico"
HEADER_PATH = ASSETS_PATH / "header.png"
USERNAME = getpass.getuser()

# Debug mode - set False for production
DEBUG_MODE = True

# Global process tracking for cleanup
active_processes = []
active_threads = []

# Download state tracking
is_downloading = False
current_download_process = None
current_temp_files = []

# Initialize Window
window = Tk()
window.title("VidMuncher 1.0.0 Beta")
window.geometry("800x500")
window.configure(bg="#5B012A")

# Lock window - disable resize and maximize
window.resizable(False, False)
window.maxsize(800, 500)
window.minsize(800, 500)

icon_set = False

icon_png_path = ASSETS_PATH / "icon.png"
if icon_png_path.exists():
    try:
        from PIL import Image, ImageTk
        icon_image = Image.open(icon_png_path)
        icon_photo = ImageTk.PhotoImage(icon_image)
        window.iconphoto(True, icon_photo)
        icon_set = True
        if DEBUG_MODE:
            print("Icon set from PNG")
    except Exception as e:
        if DEBUG_MODE:
            print(f"Failed to set PNG icon: {e}")

if not icon_set and ICON_PATH.exists():
    try:
        window.iconbitmap(ICON_PATH)
        icon_set = True
        if DEBUG_MODE:
            print("Icon set from ICO")
    except Exception as e:
        if DEBUG_MODE:
            print(f"Failed to set ICO icon: {e}")

if icon_set:
    try:
        import ctypes
        myappid = 'vidmuncher.app.1.0'
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        if DEBUG_MODE:
            print("App ID set for taskbar")
    except:
        pass

header_bg = Frame(window, bg="#2D0017", height=80)
header_bg.place(x=0, y=0, width=800, height=80)

header_image = PhotoImage(file=HEADER_PATH)
Label(window, image=header_image, bg="#2D0017").place(x=20, y=12)

url_var = StringVar()
url_entry = Entry(window, textvariable=url_var, font=("Poppins", 10), bg="#2D0017",
                  fg="#725B67", insertbackground="#EFEFEF", relief=FLAT)
url_entry.place(x=20, y=100, width=600, height=35)
url_entry.insert(0, "Input or Paste Video URL...")

def clear_placeholder(event):
    if url_entry.get() == "Input or Paste Video URL...":
        url_var.set("")
        url_entry.config(fg="#EFEFEF")

def restore_placeholder(event):
    if url_entry.get() == "":
        url_entry.insert(0, "Input or Paste Video URL...")
        url_entry.config(fg="#725B67")

url_entry.bind("<FocusIn>", clear_placeholder)
url_entry.bind("<FocusOut>", restore_placeholder)

video_info = Text(window, wrap=WORD, bg="#2D0017", fg="#EFEFEF", font=("Poppins", 9), relief=FLAT,
                 state=DISABLED, cursor="arrow")
video_info.place(x=20, y=150, width=460, height=130)
def set_video_info_placeholder():
    """Set placeholder text for video info area"""
    video_info.config(state=NORMAL)
    video_info.delete("1.0", END)
    video_info.insert(END, "Video Information")
    video_info.config(fg="#725B67")  # Gray color for placeholder
    video_info.config(state=DISABLED)

set_video_info_placeholder()

def on_mousewheel(event):
    video_info.yview_scroll(int(-1*(event.delta/120)), "units")

video_info.bind("<MouseWheel>", on_mousewheel)

def on_window_mousewheel(event):
    x, y = window.winfo_pointerx() - window.winfo_rootx(), window.winfo_pointery() - window.winfo_rooty()
    if 20 <= x <= 480 and 150 <= y <= 280:
        video_info.yview_scroll(int(-1*(event.delta/120)), "units")

window.bind("<MouseWheel>", on_window_mousewheel)

thumbnail_label = Label(window, bg="#2D0017")
thumbnail_label.place(x=500, y=150, width=260, height=130)
combo_style = ttk.Style()
combo_style.theme_use('clam')

# Configure combobox style
combo_style.configure('Custom.TCombobox',
                     fieldbackground='#2D0017',    # Background field
                     background='#89003E',         # Button background
                     foreground='#EFEFEF',         # Text color
                     arrowcolor='#EFEFEF',         # Arrow color
                     bordercolor='#2D0017',        # Border color
                     lightcolor='#2D0017',         # Light border
                     darkcolor='#2D0017',          # Dark border
                     borderwidth=1,
                     relief='flat')

# Configure combobox dropdown style
combo_style.map('Custom.TCombobox',
               fieldbackground=[('readonly', '#2D0017')],
               selectbackground=[('readonly', '#2D0017')],
               selectforeground=[('readonly', '#EFEFEF')])

# Configure dropdown listbox
combo_style.configure('Custom.TCombobox.Listbox',
                     background='#2D0017',
                     foreground='#EFEFEF',
                     selectbackground='#89003E',
                     selectforeground='#EFEFEF',
                     borderwidth=0)

try:
    window.option_add('*TCombobox*Listbox.Background', '#2D0017')
    window.option_add('*TCombobox*Listbox.Foreground', '#EFEFEF')
    window.option_add('*TCombobox*Listbox.selectBackground', '#89003E')
    window.option_add('*TCombobox*Listbox.selectForeground', '#EFEFEF')
    window.option_add('*TCombobox*Listbox.font', 'Poppins 9')
except:
    pass

Label(window, text="Select Preset", font=("Poppins", 10), bg="#5B012A", fg="#EFEFEF").place(x=20, y=300)
preset_combo = ttk.Combobox(window, state="readonly", font=("Poppins", 9),
                           style='Custom.TCombobox', values=[
    "Best Quality", "1080p", "720p", "480p", "360p", "Audio (wav)", "Audio (mp3)"
])
preset_combo.place(x=150, y=300, width=400, height=30)
preset_combo.current(0)
h264_var = BooleanVar()
h264_var.set(True)  # Default: enabled (re-encode to H.264)

h264_checkbox = Checkbutton(
    window,
    text="H.264",
    variable=h264_var,
    font=("Poppins", 10, "bold"),
    bg="#5B012A",
    fg="#EFEFEF",
    selectcolor="#2D0017",
    activebackground="#5B012A",
    activeforeground="#EFEFEF",
    relief=FLAT,
    borderwidth=0,
    highlightthickness=0
)
h264_checkbox.place(x=570, y=300)

def on_preset_change(_event):
    selected = preset_combo.get()
    if DEBUG_MODE:
        print(f"Preset changed to: {selected}")


    if "Audio" in selected:
        h264_var.set(False)
        h264_checkbox.config(state="disabled")
    else:
        h264_checkbox.config(state="normal")

    # Update preview path jika ada video data
    if 'title' in video_data:
        update_preview_path()

def update_preview_path():
    """Update preview path berdasarkan preset dan H.264 status"""
    if 'title' in video_data:
        title = video_data['title']

        # Sanitize filename
        safe_title = title
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for char in invalid_chars:
            safe_title = safe_title.replace(char, '_')


        if len(safe_title) > 100:
            safe_title = safe_title[:100]


        h264_enabled = h264_var.get()
        ext = get_extension_from_preset(preset_combo.get(), h264_enabled)

        filename_with_ext = f"{safe_title}.{ext}"
        full_path_with_ext = f"C:/Users/<USER>/Downloads/{filename_with_ext}"
        unique_full_path = get_unique_filename(full_path_with_ext)
        unique_name_without_ext = os.path.splitext(os.path.basename(unique_full_path))[0]
        unique_preview_path = f"C:/Users/<USER>/Downloads/{unique_name_without_ext}"

        save_path_var.set(unique_preview_path)

        if DEBUG_MODE:
            print(f"Updated preview path: {unique_preview_path} (will be {ext})")

def on_h264_change():
    """Handle H.264 checkbox change"""
    if DEBUG_MODE:
        print(f"H.264 encoding: {'Enabled' if h264_var.get() else 'Disabled'}")

    # Update preview path jika ada video data
    if 'title' in video_data:
        update_preview_path()

preset_combo.bind('<<ComboboxSelected>>', on_preset_change)
h264_checkbox.config(command=on_h264_change)

# Save Location
Label(window, text="Save Location", font=("Poppins", 10), bg="#5B012A", fg="#EFEFEF").place(x=20, y=340)
save_path_var = StringVar()
save_entry = Entry(window, textvariable=save_path_var, font=("Poppins", 9), bg="#2D0017",
                   fg="#EFEFEF", relief=FLAT, insertbackground="#EFEFEF")
save_entry.place(x=150, y=340, width=400, height=30)



progress_frame = Frame(window, bg="#2D0017")
progress_frame.place(x=20, y=460, width=760, height=25)

progress_canvas = Canvas(progress_frame, bg="#2D0017", highlightthickness=0, height=25)
progress_canvas.place(relwidth=1, relheight=1)

progress_bg = progress_canvas.create_rectangle(0, 0, 760, 25, fill="#2D0017", outline="#2D0017")
progress_fill = progress_canvas.create_rectangle(0, 0, 0, 25, fill="#89003E", outline="#89003E")

progress_text_item = progress_canvas.create_text(380, 12.5, text="", font=("Poppins", 9), fill="#EFEFEF", anchor=CENTER)

def get_extension_from_preset(preset, h264_enabled=True):
    """Get file extension based on selected preset"""
    if "Audio" in preset:
        if "wav" in preset:
            return "wav"
        else:
            return "mp3"
    else:
        if h264_enabled:
            return "mp4"
        else:
            return "%(ext)s"

def get_unique_filename_without_ext(base_path):
    possible_extensions = [".mp4", ".webm", ".mkv", ".m4v", ".avi"]

    counter = 0
    while True:
        if counter == 0:
            test_base = base_path
        else:
            directory = os.path.dirname(base_path)
            filename = os.path.basename(base_path)
            test_base = os.path.join(directory, f"{filename} ({counter})")


        file_exists = False
        for ext in possible_extensions:
            if os.path.exists(f"{test_base}{ext}"):
                file_exists = True
                break

        if not file_exists:
            return test_base

        counter += 1

def get_unique_filename(filepath):
    """Generate unique filename if file exists by adding (1), (2), etc"""
    if not os.path.exists(filepath):
        return filepath

    # Split path into directory, name, and extension
    directory = os.path.dirname(filepath)
    filename = os.path.basename(filepath)
    name, ext = os.path.splitext(filename)

    counter = 1
    while True:
        # Generate new filename with counter
        new_name = f"{name} ({counter}){ext}"
        new_path = os.path.join(directory, new_name)

        if not os.path.exists(new_path):
            if DEBUG_MODE:
                print(f"Generated unique filename: {new_path}")
            return new_path

        counter += 1

        # Safety limit to prevent infinite loop
        if counter > 999:
            if DEBUG_MODE:
                print(f"Warning: Reached counter limit for {filepath}")
            break

    # Fallback with timestamp if counter limit reached
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    fallback_name = f"{name}_{timestamp}{ext}"
    fallback_path = os.path.join(directory, fallback_name)

    if DEBUG_MODE:
        print(f"Using timestamp fallback: {fallback_path}")

    return fallback_path

def detect_best_encoder():
    """Detect best available H264 encoder with GPU priority"""

    # List of encoders with priority (GPU first, CPU last)
    encoders_to_test = [
        {
            "encoder": "h264_nvenc",
            "name": "NVIDIA NVENC",
            "hwaccel": "cuda",
            "test_args": ["-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1", "-t", "1"]
        },
        {
            "encoder": "h264_amf",
            "name": "AMD AMF",
            "hwaccel": "d3d11va",
            "test_args": ["-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1", "-t", "1"]
        },
        {
            "encoder": "h264_qsv",
            "name": "Intel Quick Sync",
            "hwaccel": "qsv",
            "test_args": ["-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1", "-t", "1"]
        },
        {
            "encoder": "libx264",
            "name": "CPU (Software)",
            "hwaccel": None,
            "test_args": ["-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1", "-t", "1"]
        }
    ]

    for encoder_info in encoders_to_test:
        try:
            # Test command to check encoder availability
            test_cmd = [str(FFMPEG_PATH)]

            # Add hwaccel if available
            if encoder_info["hwaccel"]:
                test_cmd.extend(["-hwaccel", encoder_info["hwaccel"]])

            # Add test input
            test_cmd.extend(encoder_info["test_args"])

            # Add encoder
            test_cmd.extend(["-c:v", encoder_info["encoder"]])

            # Add minimal settings and output to null
            if "nvenc" in encoder_info["encoder"]:
                test_cmd.extend(["-preset", "p1", "-cq", "30"])
            elif "amf" in encoder_info["encoder"]:
                test_cmd.extend(["-quality", "speed", "-qp_i", "30"])
            elif "qsv" in encoder_info["encoder"]:
                test_cmd.extend(["-preset", "veryfast", "-global_quality", "30"])
            else:
                test_cmd.extend(["-preset", "ultrafast", "-crf", "30"])

            # Output to null device
            test_cmd.extend(["-f", "null", "-"])

            if DEBUG_MODE:
                print(f"Testing encoder: {encoder_info['name']} ({encoder_info['encoder']})")

            # Run test with short timeout
            result = subprocess.run(
                test_cmd,
                capture_output=True,
                text=True,
                timeout=10,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            if result.returncode == 0:
                if DEBUG_MODE:
                    print(f"✅ Encoder available: {encoder_info['name']}")
                return encoder_info
            else:
                if DEBUG_MODE:
                    print(f"❌ Encoder failed: {encoder_info['name']} - {result.stderr[:100]}")

        except subprocess.TimeoutExpired:
            if DEBUG_MODE:
                print(f"⏱️ Encoder test timeout: {encoder_info['name']}")
            continue
        except Exception as e:
            if DEBUG_MODE:
                print(f"❌ Encoder test error: {encoder_info['name']} - {str(e)}")
            continue

    # Fallback to CPU if all fail
    if DEBUG_MODE:
        print("⚠️ All GPU encoders failed, using CPU fallback")

    return {
        "encoder": "libx264",
        "name": "CPU (Software)",
        "hwaccel": None
    }

def update_progress(text, progress=None, error=False):
    # Update canvas text (tanpa background)
    color = "#FF5050" if error else "#EFEFEF"
    progress_canvas.itemconfig(progress_text_item, text=text, fill=color)

    if progress is not None:
        # Update custom canvas progress bar
        width = int((progress / 100) * 760)  # Calculate fill width based on percentage
        progress_canvas.coords(progress_fill, 0, 0, width, 25)
    window.update_idletasks()

def browse_save_path():
    h264_enabled = h264_var.get()
    extension = get_extension_from_preset(preset_combo.get(), h264_enabled)

    # Default filename without extension for preview
    current_path = save_path_var.get()
    default_name = os.path.basename(current_path) if current_path else "video"

    path = filedialog.asksaveasfilename(
        defaultextension=f".{extension}",
        initialfile=f"{default_name}.{extension}",
        filetypes=[("Media Files", f"*.{extension}")]
    )
    if path:
        # Save path without extension for GUI preview
        path_without_ext = os.path.splitext(path)[0]
        save_path_var.set(path_without_ext)

Button(window, text="Browse", font=("Poppins", 10, "bold"), bg="#89003E", fg="white", 
       relief=FLAT, command=browse_save_path).place(x=570, y=340, width=120, height=30)

video_data = {}
thumbnail_imgtk = None

# Memory management functions
def clear_thumbnail():
    """Clear previous thumbnail to prevent memory leaks"""
    global thumbnail_imgtk
    if thumbnail_imgtk:
        thumbnail_imgtk = None
        # Force garbage collection of image
        import gc
        gc.collect()

def reset_video_data():
    """Reset video data and clear thumbnail"""
    video_data.clear()
    clear_thumbnail()
    # Clear thumbnail display
    thumbnail_label.config(image="")
    thumbnail_label.config(bg="#2D0017")

def cleanup_file_with_timeout(filepath, max_attempts=3, total_timeout=2):
    """Optimized file cleanup with timeout"""
    start_time = time.time()
    for attempt in range(max_attempts):
        if time.time() - start_time > total_timeout:
            break  # Don't exceed total timeout
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                return True
        except PermissionError:
            time.sleep(0.1)  # Short sleep
        except Exception:
            break  # Don't retry on other errors
    return False

def cleanup_finished_processes():
    """Remove finished processes and dead threads from tracking lists"""
    global active_processes, active_threads

    # Remove finished processes
    active_processes[:] = [p for p in active_processes if p.poll() is None]

    # Remove dead threads
    active_threads[:] = [t for t in active_threads if t.is_alive()]

# Function to set button states
def set_button_states(analyze_enabled=True, download_enabled=False):
    """Set state of Analyze and Download buttons"""
    if analyze_enabled:
        analyze_button.config(state=NORMAL, bg="#89003E")
    else:
        analyze_button.config(state=DISABLED, bg="#672845")

    if download_enabled:
        download_button.config(state=NORMAL, bg="#89003E")
    else:
        download_button.config(state=DISABLED, bg="#672845")

def analyze_video():
    url = url_var.get().strip()
    if not url or url == "Input or Paste Video URL...":
        update_progress("URL cannot be empty!", error=True)
        return

    # Disable buttons during analyze
    set_button_states(analyze_enabled=False, download_enabled=False)

    def task():
        update_progress("Getting information...", progress=0)
        try:
            # Enhanced command with same options as download
            analyze_cmd = [
                str(YTDLP_PATH), "--dump-json", url,
                "--user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "--referer", "https://www.youtube.com/",
                "--extractor-retries", "3",
                "--fragment-retries", "3",
                "--retry-sleep", "1",
                "--no-check-certificate",
                "--no-playlist"  # Prevent playlist processing during analyze
            ]

            if DEBUG_MODE:
                print(f"Analyze command: {' '.join(analyze_cmd)}")

            result = subprocess.run(analyze_cmd,
                                 capture_output=True, text=True, check=True,
                                 timeout=30,  # Add timeout to prevent hanging
                                 creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
            info = json.loads(result.stdout)
            video_data.clear()
            video_data.update(info)
            
            title = info.get("title", "N/A")
            desc = info.get("description", "")
            thumbnail_url = info.get("thumbnail", "")
            
            # Temporarily enable to update content, then disable again
            video_info.config(state=NORMAL)
            video_info.delete("1.0", END)
            video_info.insert(END, f"{title}\n\n{desc}")
            video_info.config(fg="#EFEFEF")  # Change to normal text color
            video_info.config(state=DISABLED)  # Back to read-only

            # Clear previous thumbnail first
            clear_thumbnail()

            # Download thumbnail asynchronously to prevent GUI blocking
            def download_thumbnail_async(url):
                def task():
                    try:
                        # Download with timeout to prevent hanging
                        img_data = requests.get(url, timeout=10).content
                        img = Image.open(BytesIO(img_data))
                        img = img.resize((260, 130), Image.Resampling.LANCZOS)

                        # Update UI in main thread
                        def update_thumbnail():
                            global thumbnail_imgtk
                            thumbnail_imgtk = ImageTk.PhotoImage(img)
                            thumbnail_label.config(image=thumbnail_imgtk)

                        window.after(0, update_thumbnail)
                    except Exception as e:
                        if DEBUG_MODE:
                            print(f"Thumbnail download failed: {e}")
                        # Show placeholder on failure
                        window.after(0, lambda: thumbnail_label.config(image=""))

                threading.Thread(target=task, daemon=True, name="ThumbnailThread").start()

            download_thumbnail_async(thumbnail_url)

            # Set default save path dengan sanitized filename
            h264_enabled = h264_var.get()
            ext = get_extension_from_preset(preset_combo.get(), h264_enabled)

            # Sanitize filename - hapus karakter bermasalah
            safe_title = title
            # Karakter yang bermasalah di Windows
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
            for char in invalid_chars:
                safe_title = safe_title.replace(char, '_')

            # Batasi panjang filename
            if len(safe_title) > 100:
                safe_title = safe_title[:100]

            # Generate unique filename dengan ekstensi untuk checking
            filename_with_ext = f"{safe_title}.{ext}"
            full_path_with_ext = f"C:/Users/<USER>/Downloads/{filename_with_ext}"
            unique_full_path = get_unique_filename(full_path_with_ext)

            # Extract nama tanpa ekstensi dari unique path untuk preview GUI
            unique_name_without_ext = os.path.splitext(os.path.basename(unique_full_path))[0]
            unique_preview_path = f"C:/Users/<USER>/Downloads/{unique_name_without_ext}"

            save_path_var.set(unique_preview_path)

            update_progress("Ready to download", progress=0)
            # Enable tombol download setelah analyze berhasil
            set_button_states(analyze_enabled=True, download_enabled=True)
        except subprocess.TimeoutExpired:
            update_progress("Timeout - URL took too long to analyze", error=True)
            if DEBUG_MODE:
                print("Analyze timeout after 30 seconds")
            set_video_info_placeholder()  # Reset to placeholder
            set_button_states(analyze_enabled=True, download_enabled=False)
        except subprocess.CalledProcessError as e:
            if "This video is unavailable" in e.stderr:
                update_progress("Video unavailable or private", error=True)
            elif "Video unavailable" in e.stderr:
                update_progress("Video not found or restricted", error=True)
            elif "Sign in to confirm your age" in e.stderr:
                update_progress("Age-restricted video", error=True)
            else:
                update_progress("Failed to get video info", error=True)
            if DEBUG_MODE:
                print(f"yt-dlp error: {e.stderr}")
            set_video_info_placeholder()  # Reset to placeholder
            reset_video_data()  # Clear video data and thumbnail
            set_button_states(analyze_enabled=True, download_enabled=False)
        except Exception as e:
            update_progress("Invalid URL or network error", error=True)
            if DEBUG_MODE:
                print(f"Analyze error: {str(e)}")
            # Re-enable analyze button jika error
            set_video_info_placeholder()  # Reset to placeholder
            set_button_states(analyze_enabled=True, download_enabled=False)

    # Create daemon thread untuk auto-cleanup
    thread = threading.Thread(target=task, daemon=True, name="AnalyzeThread")
    active_threads.append(thread)
    thread.start()

    # Cleanup finished processes periodically
    cleanup_finished_processes()

# Tombol Analyze
analyze_button = Button(window, text="Analyze", font=("Poppins", 10, "bold"), bg="#89003E", fg="white",
                       relief=FLAT, command=analyze_video)
analyze_button.place(x=640, y=100, width=120, height=35)

def download_video():
    url = url_var.get().strip()
    out_path = save_path_var.get()
    preset = preset_combo.get()

    if not url or url == "Input or Paste Video URL...":
        update_progress("URL cannot be empty!", error=True)
        return

    # Disable tombol saat download dan show cancel button
    set_button_states(analyze_enabled=False, download_enabled=False)
    show_cancel_button(True)  # Show cancel button

    # Set download state
    global is_downloading, current_temp_files
    is_downloading = True
    current_temp_files.clear()  # Reset temp files list

    def task():
        # Tambahkan ekstensi berdasarkan preset dan status H.264
        h264_enabled = h264_var.get()
        extension = get_extension_from_preset(preset, h264_enabled)

        if extension == "%(ext)s":
            # Untuk video tanpa H.264, biarkan yt-dlp menentukan ekstensi
            out_path_with_ext = out_path  # Tanpa ekstensi, yt-dlp akan menambahkan
            unique_out_path = get_unique_filename_without_ext(out_path)
        else:
            # Untuk audio atau video dengan H.264, gunakan ekstensi yang sudah ditentukan
            out_path_with_ext = f"{out_path}.{extension}"
            unique_out_path = get_unique_filename(out_path_with_ext)

        if DEBUG_MODE:
            print(f"Original path (no ext): {out_path}")
            print(f"Extension: {extension}")
            print(f"Path with extension: {out_path_with_ext}")
            print(f"Final unique path: {unique_out_path}")

        # Use unique path untuk download
        out_path_final = unique_out_path

        update_progress("Downloading...", progress=0)

        # Base command dengan opsi untuk mengatasi YouTube restrictions
        ydl_cmd = [
            str(YTDLP_PATH), url, "-o", out_path_final,
            "--no-playlist", "--progress",
            "--user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "--referer", "https://www.youtube.com/",
            "--extractor-retries", "3",
            "--fragment-retries", "3",
            "--retry-sleep", "1",
            "--no-check-certificate",
            "--ffmpeg-location", str(FFMPEG_PATH)  # Specify ffmpeg location
        ]

        if "Audio" in preset:
            ydl_cmd += ["--extract-audio", "--audio-format", "mp3" if "mp3" in preset else "wav"]
        elif preset != "Best Quality":
            height = preset.replace("p", "")
            # Format selection untuk resolusi spesifik - prioritaskan resolusi yang diminta
            ydl_cmd += ["-f", f"bestvideo[height={height}]+bestaudio/bestvideo[height<={height}]+bestaudio/best[height<={height}]"]
        else:
            # Best Quality - prioritaskan 1080p+ dengan format yang kompatibel
            ydl_cmd += ["-f", "bestvideo[height>=1080]+bestaudio/bestvideo[height>=720]+bestaudio/bestvideo+bestaudio/best"]

        try:
            process = subprocess.Popen(ydl_cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                     text=True, bufsize=1, universal_newlines=True,
                                     creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)

            # Track process untuk cleanup dan cancellation
            active_processes.append(process)
            global current_download_process
            current_download_process = process

            # Multi-stream progress tracking
            stream_progress = {}  # Track progress per stream
            current_stream = None
            total_streams = 0
            last_overall_progress = 0
            line_count = 0

            for line in process.stdout:
                # Check for cancellation
                if not is_downloading:
                    if DEBUG_MODE:
                        print("Download cancelled by user")
                    break

                line = line.strip()
                line_count += 1

                # Debug output
                if DEBUG_MODE:
                    print(f"yt-dlp: {line}")

                # Detect new stream download
                if "[download] Destination:" in line:
                    # Extract stream identifier from filename
                    if ".f" in line and (".mp4" in line or ".webm" in line or ".m4a" in line):
                        # This is a separate stream (video/audio)
                        stream_match = re.search(r'\.f(\d+)\.', line)
                        if stream_match:
                            current_stream = stream_match.group(1)
                            stream_progress[current_stream] = 0
                            total_streams = len(stream_progress)
                            if DEBUG_MODE:
                                print(f"Detected stream: {current_stream}, Total streams: {total_streams}")
                    else:
                        # Single file download
                        current_stream = "single"
                        stream_progress[current_stream] = 0
                        total_streams = 1

                # Progress parsing dengan multi-stream support
                elif line and "%" in line and "[download]" in line:
                    try:
                        percent_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                        if percent_match:
                            progress = float(percent_match.group(1))

                            # Update current stream progress
                            if current_stream:
                                stream_progress[current_stream] = progress

                            # Calculate overall progress
                            if total_streams > 0:
                                # For multiple streams: average progress across all streams
                                if total_streams > 1:
                                    total_progress = sum(stream_progress.values())
                                    overall_progress = total_progress / total_streams

                                    # Determine current phase
                                    completed_count = sum(1 for p in stream_progress.values() if p >= 100)
                                    if completed_count == 0:
                                        phase = f"Video stream"
                                    elif completed_count == 1:
                                        phase = f"Audio stream"
                                    else:
                                        phase = f"Finalizing"
                                else:
                                    # Single stream
                                    overall_progress = progress
                                    phase = "Downloading"

                                # Cari speed info
                                speed_match = re.search(r'(\d+(?:\.\d+)?[KMGT]?i?B/s)', line)
                                speed = speed_match.group(1) if speed_match else "Unknown"

                                # Update progress jika ada perubahan signifikan
                                if abs(overall_progress - last_overall_progress) >= 0.5 or overall_progress == 100:
                                    if total_streams > 1:
                                        update_progress(f"{phase} - {speed} - {overall_progress:.1f}%", progress=overall_progress)
                                    else:
                                        update_progress(f"{speed} - {overall_progress:.1f}%", progress=overall_progress)
                                    last_overall_progress = overall_progress

                    except Exception as e:
                        if DEBUG_MODE:
                            print(f"Progress parsing error: {e}")
                        # Simple fallback parsing - hanya untuk stream yang sedang aktif
                        if current_stream and (f".f{current_stream}." in line or current_stream == "single"):
                            try:
                                parts = line.split()
                                for part in parts:
                                    if "%" in part:
                                        progress = float(part.replace("%", ""))
                                        # Update stream progress dan hitung overall
                                        if current_stream:
                                            stream_progress[current_stream] = progress

                                        # Hitung overall progress
                                        if total_streams > 0:
                                            overall_progress = sum(stream_progress.values()) / total_streams
                                            if total_streams > 1:
                                                update_progress(f"Downloading streams - {overall_progress:.1f}%", progress=overall_progress)
                                            else:
                                                update_progress(f"Downloading - {progress:.1f}%", progress=progress)
                                        break
                            except:
                                pass

                # Status updates untuk merger dan lainnya
                elif line and any(keyword in line.lower() for keyword in ['merger', 'merging']):
                    update_progress("Merging video and audio...", progress=95)
                elif line and any(keyword in line.lower() for keyword in ['extracting', 'converting']):
                    if "webpage" not in line.lower():
                        update_progress(line[:50] + "..." if len(line) > 50 else line, progress=None)

            process.wait()

            # Remove from active processes when done
            if process in active_processes:
                active_processes.remove(process)

            if process.returncode == 0:
                if "Audio" not in preset:
                    # Check apakah user ingin H.264 encoding
                    if h264_var.get():
                        # User memilih untuk encode ke H264
                        update_progress("Preparing H264 encoding...", progress=0)

                        # Cari file yang benar-benar ada (bisa .mp4, .webm, .mkv, dll)
                        actual_file = None
                        base_path = out_path_final.replace(".mp4", "")

                        # Kemungkinan ekstensi file hasil download
                        possible_extensions = [".mp4", ".webm", ".mkv", ".m4v"]

                        for ext in possible_extensions:
                            test_path = base_path + ext
                            if os.path.exists(test_path):
                                actual_file = test_path
                                break

                        # Jika tidak ditemukan, coba cari file dengan nama yang mirip
                        if not actual_file:
                            # Escape special characters untuk glob
                            safe_base = base_path.replace('[', r'\[').replace(']', r'\]')
                            pattern = safe_base + ".*"
                            matches = glob.glob(pattern)
                            if matches:
                                # Ambil file pertama yang ditemukan
                                actual_file = matches[0]

                        if not actual_file or not os.path.exists(actual_file):
                            update_progress("Source file not found for encoding!", error=True)
                            if DEBUG_MODE:
                                print(f"Expected file: {out_path_final}")
                                print(f"Base path: {base_path}")
                                print(f"Checked extensions: {possible_extensions}")
                            # Re-enable tombol setelah error
                            set_button_states(analyze_enabled=True, download_enabled=True)
                            return

                        if DEBUG_MODE:
                            print(f"Found source file: {actual_file}")

                        # Pastikan output path berakhiran .mp4
                        final_output = out_path_final if out_path_final.endswith('.mp4') else base_path + ".mp4"
                        temp_path = final_output.replace(".mp4", "_temp.mp4")

                        # Track temp file untuk cleanup
                        current_temp_files.append(temp_path)

                        # Deteksi encoder terbaik yang tersedia
                        encoder_info = detect_best_encoder()
                        encoder = encoder_info["encoder"]
                        encoder_name = encoder_info["name"]
                        hwaccel = encoder_info["hwaccel"]

                        update_progress(f"Encoding to H264 ({encoder_name}) - 0%", progress=0)

                        # Build FFmpeg command berdasarkan encoder yang terdeteksi
                        ffmpeg_cmd = [str(FFMPEG_PATH)]

                        # Add hardware acceleration jika tersedia
                        if hwaccel:
                            ffmpeg_cmd.extend(["-hwaccel", hwaccel])

                        # Input file
                        ffmpeg_cmd.extend(["-i", actual_file])

                        # Video encoder dan settings
                        ffmpeg_cmd.extend(["-c:v", encoder])

                        # Encoder-specific settings
                        if "nvenc" in encoder:
                            # NVIDIA NVENC settings
                            ffmpeg_cmd.extend([
                                "-preset", "p4",                # Balanced preset
                                "-rc", "vbr",                   # Variable bitrate
                                "-cq", "23",                    # Constant quality
                                "-b:v", "0"                     # Let NVENC decide bitrate
                            ])
                        elif "amf" in encoder:
                            # AMD AMF settings
                            ffmpeg_cmd.extend([
                                "-quality", "balanced",         # Balanced quality/speed
                                "-rc", "cqp",                   # Constant quantization
                                "-qp_i", "23",                  # I-frame quantization
                                "-qp_p", "23",                  # P-frame quantization
                                "-qp_b", "23"                   # B-frame quantization
                            ])
                        elif "qsv" in encoder:
                            # Intel Quick Sync settings
                            ffmpeg_cmd.extend([
                                "-preset", "medium",            # Balanced preset
                                "-global_quality", "23",        # Quality setting
                                "-look_ahead", "1"              # Look ahead
                            ])
                        else:
                            # CPU libx264 settings (fallback)
                            ffmpeg_cmd.extend([
                                "-preset", "medium",            # Balanced preset
                                "-crf", "23"                    # Constant rate factor
                            ])

                        # Audio settings (sama untuk semua encoder)
                        ffmpeg_cmd.extend([
                            "-c:a", "aac",                      # Audio codec
                            "-b:a", "128k",                     # Audio bitrate
                            "-movflags", "+faststart",          # Web optimization
                            "-avoid_negative_ts", "make_zero",  # Fix timestamp issues
                            "-y", temp_path                     # Output file
                        ])

                        if DEBUG_MODE:
                            print(f"FFmpeg command: {' '.join(ffmpeg_cmd)}")

                        try:
                            # Run FFmpeg dengan real-time progress monitoring
                            ffmpeg_process = subprocess.Popen(
                                ffmpeg_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.STDOUT,  # Redirect stderr to stdout untuk monitoring
                                text=True,
                                bufsize=1,
                                universal_newlines=True,
                                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                            )

                            # Track FFmpeg process untuk cleanup
                            active_processes.append(ffmpeg_process)

                            # Real-time progress monitoring dari FFmpeg output
                            duration_seconds = None
                            encoding_progress = 0

                            for line in ffmpeg_process.stdout:
                                # Check for cancellation
                                if not is_downloading:
                                    if DEBUG_MODE:
                                        print("FFmpeg encoding cancelled by user")
                                    break

                                line = line.strip()
                                if DEBUG_MODE:
                                    print(f"FFmpeg: {line}")

                                # Extract duration dari FFmpeg output
                                if "Duration:" in line and not duration_seconds:
                                    try:
                                        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+)\.(\d+)', line)
                                        if duration_match:
                                            h, m, s, ms = map(int, duration_match.groups())
                                            duration_seconds = h * 3600 + m * 60 + s + ms / 100
                                            if DEBUG_MODE:
                                                print(f"Video duration: {duration_seconds} seconds")
                                    except:
                                        pass

                                # Extract current time dari FFmpeg output
                                elif "time=" in line and duration_seconds:
                                    try:
                                        time_match = re.search(r'time=(\d+):(\d+):(\d+)\.(\d+)', line)
                                        if time_match:
                                            h, m, s, ms = map(int, time_match.groups())
                                            current_seconds = h * 3600 + m * 60 + s + ms / 100
                                            progress = min(99, (current_seconds / duration_seconds) * 100)
                                            if progress > encoding_progress:
                                                encoding_progress = progress
                                                update_progress(f"Encoding H264 ({encoder_name}) - {progress:.1f}%", progress=progress)
                                    except:
                                        pass

                                # Fallback progress untuk case dimana duration tidak terdeteksi
                                elif encoding_progress == 0 and any(keyword in line.lower() for keyword in ['frame=', 'fps=', 'bitrate=']):
                                    encoding_progress = min(50, encoding_progress + 10)
                                    update_progress(f"Encoding H264 ({encoder_name}) - {encoding_progress}%", progress=encoding_progress)

                            # Wait for process completion
                            ffmpeg_process.wait()

                            # Remove from active processes when done
                            if ffmpeg_process in active_processes:
                                active_processes.remove(ffmpeg_process)

                            if DEBUG_MODE:
                                print(f"FFmpeg process completed with return code: {ffmpeg_process.returncode}")

                            if ffmpeg_process.returncode == 0:
                                # Encoding berhasil - finalisasi
                                update_progress(f"Finalizing H264 ({encoder_name})...", progress=99)

                                try:
                                    if os.path.exists(temp_path):
                                        # Hapus file asli jika berbeda dari output final
                                        if actual_file != final_output and os.path.exists(actual_file):
                                            os.remove(actual_file)
                                            if DEBUG_MODE:
                                                print(f"Removed source file: {actual_file}")

                                        # Pindahkan file hasil encoding ke lokasi final
                                        shutil.move(temp_path, final_output)
                                        if DEBUG_MODE:
                                            print(f"Moved encoded file to: {final_output}")

                                        complete_download(success=True, message="Download Complete!")
                                    else:
                                        # Check if this is a cancellation
                                        if not is_downloading:
                                            if DEBUG_MODE:
                                                print("Encoding output check cancelled by user")
                                            return  # Don't show error, cancel function will handle UI
                                        else:
                                            update_progress("Encoding Error - Output file not found!", error=True)
                                            if DEBUG_MODE:
                                                print(f"Expected output file not found: {temp_path}")
                                            # Re-enable tombol setelah error
                                            set_button_states(analyze_enabled=True, download_enabled=True)
                                except Exception as e:
                                    # Check if this is a cancellation
                                    if not is_downloading:
                                        if DEBUG_MODE:
                                            print("File move cancelled by user")
                                        return  # Don't show error, cancel function will handle UI
                                    else:
                                        update_progress("File move error!", error=True)
                                        if DEBUG_MODE:
                                            print(f"Move error: {str(e)}")
                                        # Re-enable tombol setelah error
                                        set_button_states(analyze_enabled=True, download_enabled=True)
                            else:
                                # Check if this is a cancellation
                                if not is_downloading:
                                    # This was a cancellation, not an encoding failure
                                    if DEBUG_MODE:
                                        print("H264 encoding was cancelled by user")
                                    return  # Don't show error, cancel function will handle UI
                                else:
                                    update_progress("H264 Encoding Failed!", error=True)
                                    if DEBUG_MODE:
                                        print(f"FFmpeg failed with return code: {ffmpeg_process.returncode}")
                                    # Re-enable tombol setelah error
                                    set_button_states(analyze_enabled=True, download_enabled=True)

                            # Clean up temp file
                            if os.path.exists(temp_path):
                                os.remove(temp_path)

                        except Exception as e:
                            # Check if this is a cancellation
                            if not is_downloading:
                                if DEBUG_MODE:
                                    print("Encoding exception due to cancellation")
                                return  # Don't show error, cancel function will handle UI
                            else:
                                update_progress("Encoding Error!", error=True)
                                if DEBUG_MODE:
                                    print(f"Encoding exception: {str(e)}")

                                # Clean up FFmpeg process if still running
                                try:
                                    if 'ffmpeg_process' in locals() and ffmpeg_process in active_processes:
                                        active_processes.remove(ffmpeg_process)
                                    if 'ffmpeg_process' in locals() and ffmpeg_process.poll() is None:
                                        ffmpeg_process.terminate()
                                except:
                                    pass

                                # Clean up temp file
                                if os.path.exists(temp_path):
                                    os.remove(temp_path)

                                # Re-enable tombol setelah error
                                set_button_states(analyze_enabled=True, download_enabled=True)
                    else:
                        # User memilih untuk TIDAK encode ke H264 - skip encoding
                        # Update timestamp untuk video file agar mudah ditemukan di explorer
                        try:
                            # Cari file video yang baru di-download
                            video_file = None

                            # Tentukan base path berdasarkan apakah ekstensi sudah ditentukan atau tidak
                            if extension == "%(ext)s":
                                # File tanpa ekstensi yang sudah ditentukan
                                base_path = out_path_final
                            else:
                                # File dengan ekstensi yang sudah ditentukan
                                base_path = out_path_final.replace(f".{extension}", "")

                            # Kemungkinan ekstensi video hasil download
                            video_extensions = [".webm", ".mp4", ".mkv", ".m4v", ".avi"]

                            # Method 1: Cari file dengan ekstensi yang berbeda
                            for ext in video_extensions:
                                test_path = base_path + ext
                                if os.path.exists(test_path):
                                    video_file = test_path
                                    if DEBUG_MODE:
                                        print(f"Found video file (direct): {video_file}")
                                    break

                            # Method 2: Jika tidak ditemukan, coba glob search
                            if not video_file:
                                safe_base = base_path.replace('[', r'\[').replace(']', r'\]')
                                pattern = safe_base + ".*"
                                matches = glob.glob(pattern)
                                if DEBUG_MODE:
                                    print(f"Glob pattern: {pattern}")
                                    print(f"Glob matches: {matches}")

                                if matches:
                                    for match in matches:
                                        if any(match.lower().endswith(ext) for ext in video_extensions):
                                            video_file = match
                                            if DEBUG_MODE:
                                                print(f"Found video file (glob): {video_file}")
                                            break

                            # Method 3: Cari berdasarkan nama file yang mengandung base name
                            if not video_file:
                                try:
                                    directory = os.path.dirname(base_path)
                                    base_name = os.path.basename(base_path)

                                    if os.path.exists(directory):
                                        all_files = os.listdir(directory)
                                        if DEBUG_MODE:
                                            print(f"Searching in directory: {directory}")
                                            print(f"Base name: {base_name}")
                                            print(f"All files: {all_files}")

                                        for file in all_files:
                                            if base_name.lower() in file.lower():
                                                if any(file.lower().endswith(ext) for ext in video_extensions):
                                                    video_file = os.path.join(directory, file)
                                                    if DEBUG_MODE:
                                                        print(f"Found video file (directory scan): {video_file}")
                                                    break
                                except Exception as scan_e:
                                    if DEBUG_MODE:
                                        print(f"Directory scan error: {scan_e}")

                            if video_file and os.path.exists(video_file):
                                # Update timestamp file ke waktu sekarang
                                current_time = time.time()
                                os.utime(video_file, (current_time, current_time))

                                if DEBUG_MODE:
                                    print(f"✅ Successfully updated timestamp for video file: {video_file}")
                                    # Verify timestamp update
                                    stat_info = os.stat(video_file)
                                    print(f"New modification time: {time.ctime(stat_info.st_mtime)}")
                            else:
                                if DEBUG_MODE:
                                    print(f"❌ Video file not found for timestamp update")
                                    print(f"Expected base: {base_path}")
                                    print(f"Checked extensions: {video_extensions}")

                        except Exception as e:
                            if DEBUG_MODE:
                                print(f"Error updating video timestamp: {str(e)}")

                        complete_download(success=True, message="Download Complete!")
                        if DEBUG_MODE:
                            print("H264 encoding skipped by user choice")
                else:
                    # Audio preset - update file timestamp untuk visibility di explorer
                    try:
                        # Cari file audio yang baru di-download
                        audio_file = None

                        # Get directory dari output path
                        output_dir = os.path.dirname(out_path_final)
                        base_name = os.path.splitext(os.path.basename(out_path_final))[0]

                        if DEBUG_MODE:
                            print(f"Looking for audio file in: {output_dir}")
                            print(f"Base name: {base_name}")
                            print(f"Original out_path_final: {out_path_final}")

                        # Kemungkinan ekstensi audio
                        audio_extensions = [".mp3", ".wav", ".m4a", ".webm", ".opus"]

                        # Method 1: Direct path check
                        for ext in audio_extensions:
                            test_path = os.path.join(output_dir, base_name + ext)
                            if DEBUG_MODE:
                                print(f"Checking: {test_path}")
                            if os.path.exists(test_path):
                                audio_file = test_path
                                if DEBUG_MODE:
                                    print(f"Found audio file (direct): {audio_file}")
                                break

                        # Method 2: Glob search jika tidak ditemukan
                        if not audio_file:
                            if DEBUG_MODE:
                                print("Direct search failed, trying glob pattern...")

                            # Escape special characters untuk glob
                            safe_base = base_name.replace('[', r'\[').replace(']', r'\]')
                            pattern = os.path.join(output_dir, safe_base + ".*")

                            if DEBUG_MODE:
                                print(f"Glob pattern: {pattern}")

                            matches = glob.glob(pattern)
                            if DEBUG_MODE:
                                print(f"Glob matches: {matches}")

                            if matches:
                                # Ambil file audio pertama yang ditemukan
                                for match in matches:
                                    if any(match.lower().endswith(ext) for ext in audio_extensions):
                                        audio_file = match
                                        if DEBUG_MODE:
                                            print(f"Found audio file (glob): {audio_file}")
                                        break

                        # Method 3: List semua file di directory jika masih tidak ditemukan
                        if not audio_file:
                            if DEBUG_MODE:
                                print("Glob search failed, listing all files in directory...")
                            try:
                                all_files = os.listdir(output_dir)
                                if DEBUG_MODE:
                                    print(f"All files in directory: {all_files}")

                                # Cari file yang mengandung base name dan ekstensi audio
                                for file in all_files:
                                    if base_name.lower() in file.lower():
                                        if any(file.lower().endswith(ext) for ext in audio_extensions):
                                            audio_file = os.path.join(output_dir, file)
                                            if DEBUG_MODE:
                                                print(f"Found audio file (directory scan): {audio_file}")
                                            break
                            except Exception as e:
                                if DEBUG_MODE:
                                    print(f"Directory listing failed: {e}")

                        if audio_file and os.path.exists(audio_file):
                            # Update timestamp file ke waktu sekarang
                            current_time = time.time()
                            os.utime(audio_file, (current_time, current_time))

                            if DEBUG_MODE:
                                print(f"✅ Successfully updated timestamp for: {audio_file}")
                                # Verify timestamp update
                                stat_info = os.stat(audio_file)
                                print(f"New modification time: {time.ctime(stat_info.st_mtime)}")

                            complete_download(success=True, message="Audio Download Complete!")
                        else:
                            if DEBUG_MODE:
                                print(f"❌ Audio file not found for timestamp update")
                                print(f"Expected base: {base_name}")
                                print(f"Expected directory: {output_dir}")
                                print(f"Checked extensions: {audio_extensions}")
                            update_progress("Audio Download Complete!", progress=100)
                            # Re-enable tombol setelah download selesai
                            set_button_states(analyze_enabled=True, download_enabled=True)

                    except Exception as e:
                        if DEBUG_MODE:
                            print(f"Error updating audio file timestamp: {str(e)}")
                        update_progress("Audio Download Complete!", progress=100)
                        # Re-enable tombol setelah download selesai
                        set_button_states(analyze_enabled=True, download_enabled=True)
            else:
                # Check if this is a cancellation
                if not is_downloading:
                    # This was a cancellation, not an error
                    if DEBUG_MODE:
                        print("Download was cancelled by user")
                    return  # Don't show error, cancel function will handle UI
                else:
                    update_progress("Download Error!", error=True)
                    # Re-enable tombol jika error
                    set_button_states(analyze_enabled=True, download_enabled=True)
        except Exception as e:
            # Check if this is a cancellation
            if not is_downloading:
                # This was a cancellation, not an error
                if DEBUG_MODE:
                    print("Download was cancelled by user (exception caught)")
                return  # Don't show error, cancel function will handle UI
            else:
                update_progress("Download Error!", error=True)
                print(f"Error: {str(e)}")

                # Clean up any remaining processes
                for process in active_processes[:]:
                    try:
                        if process.poll() is None:
                            process.terminate()
                        active_processes.remove(process)
                    except:
                        pass

                # Re-enable tombol jika error
                set_button_states(analyze_enabled=True, download_enabled=True)

    # Create daemon thread untuk auto-cleanup
    thread = threading.Thread(target=task, daemon=True, name="DownloadThread")
    active_threads.append(thread)
    thread.start()

    # Cleanup finished processes periodically
    cleanup_finished_processes()

# Tombol Download - initially disabled
download_button = Button(window, text="Download", font=("Poppins", 11, "bold"), bg="#672845", fg="white",
                        relief=FLAT, command=download_video, state=DISABLED)
download_button.place(x=340, y=400, width=120, height=40)

# Cancel Button (hidden by default, muncul saat download)
def cancel_download():
    """Cancel ongoing download and cleanup"""
    global is_downloading, current_download_process, current_temp_files

    if DEBUG_MODE:
        print("🚫 Canceling download...")

    # Set cancellation flag FIRST
    is_downloading = False

    # Kill all yt-dlp and ffmpeg processes system-wide (aggressive approach)
    try:
        if os.name == 'nt':  # Windows
            # Kill all yt-dlp processes
            subprocess.run(['taskkill', '/F', '/IM', 'yt-dlp.exe'],
                         capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
            # Kill all ffmpeg processes
            subprocess.run(['taskkill', '/F', '/IM', 'ffmpeg.exe'],
                         capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW)
            if DEBUG_MODE:
                print("Killed all yt-dlp and ffmpeg processes")
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'yt-dlp'], capture_output=True)
            subprocess.run(['pkill', '-f', 'ffmpeg'], capture_output=True)
    except Exception as e:
        if DEBUG_MODE:
            print(f"Error killing processes system-wide: {e}")

    # Terminate current process with force
    if current_download_process and current_download_process.poll() is None:
        try:
            if DEBUG_MODE:
                print(f"Terminating main process: {current_download_process.pid}")
            current_download_process.terminate()

            # Wait for graceful termination
            try:
                current_download_process.wait(timeout=2)
                if DEBUG_MODE:
                    print("Process terminated gracefully")
            except subprocess.TimeoutExpired:
                # Force kill if doesn't terminate gracefully
                if DEBUG_MODE:
                    print("Force killing process...")
                current_download_process.kill()
                current_download_process.wait()  # Wait for kill to complete
                if DEBUG_MODE:
                    print("Process force killed")
        except Exception as e:
            if DEBUG_MODE:
                print(f"Error terminating main process: {e}")

    # Clean up all active processes with force
    for process in active_processes[:]:
        try:
            if process.poll() is None:
                if DEBUG_MODE:
                    print(f"Terminating active process: {process.pid}")
                process.terminate()

                # Wait for graceful termination
                try:
                    process.wait(timeout=1)
                except subprocess.TimeoutExpired:
                    # Force kill if doesn't terminate
                    process.kill()
                    process.wait()
                    if DEBUG_MODE:
                        print(f"Force killed process: {process.pid}")

            active_processes.remove(process)
        except Exception as e:
            if DEBUG_MODE:
                print(f"Error cleaning up process: {e}")
            # Remove from list even if cleanup failed
            try:
                active_processes.remove(process)
            except:
                pass

    # Clean up temporary files (more thorough)
    import time

    # Optimized wait time for file handles to release
    time.sleep(1.0)  # Reduced from 3.0s for better responsiveness

    # Clean tracked temp files with optimized function
    for temp_file in current_temp_files[:]:
        if cleanup_file_with_timeout(temp_file):
            if DEBUG_MODE:
                print(f"Removed temp file: {temp_file}")
        elif DEBUG_MODE:
            print(f"Failed to remove temp file: {temp_file}")

    # Also clean up common temp file patterns in download directory
    try:
        save_path = save_path_var.get()
        if save_path and os.path.exists(os.path.dirname(save_path)):
            download_dir = os.path.dirname(save_path)
            base_name = os.path.splitext(os.path.basename(save_path))[0]

            # More aggressive temp file patterns
            temp_patterns = [
                f"{base_name}_temp.mp4",
                f"{base_name}.f*.mp4",
                f"{base_name}.f*.webm",
                f"{base_name}.f*.m4a",
                f"{base_name}.f*.mp3",
                f"{base_name}.part",
                f"{base_name}.ytdl",
                f"{base_name}.temp",
                "*.f*.mp4",      # Any stream files
                "*.f*.webm",     # Any stream files
                "*.f*.m4a",      # Any audio streams
                "*.part",        # Any partial downloads
                "*.ytdl",        # Any yt-dlp temp files
                "*_temp.mp4"     # Any temp encoding files
            ]

            import glob
            for pattern in temp_patterns:
                pattern_path = os.path.join(download_dir, pattern)
                for temp_file in glob.glob(pattern_path):
                    if cleanup_file_with_timeout(temp_file, max_attempts=5, total_timeout=3):
                        if DEBUG_MODE:
                            print(f"Cleaned up temp file: {temp_file}")
                    elif DEBUG_MODE:
                        print(f"Could not remove temp file: {temp_file}")
    except Exception as e:
        if DEBUG_MODE:
            print(f"Error during temp file pattern cleanup: {e}")

    # Clear temp files list
    current_temp_files.clear()

    # Update UI using complete_download with cancellation flag
    complete_download(success=True, message="Download Canceled", is_cancellation=True)

    if DEBUG_MODE:
        print("✅ Download cancellation completed")

def show_cancel_button(show=True):
    """Show or hide cancel button"""
    if show:
        cancel_button.place(x=470, y=400, width=30, height=40)  # Right next to download button
    else:
        cancel_button.place_forget()  # Hide button

def complete_download(success=True, message="Download Complete!", is_cancellation=False):
    """Complete download and reset UI state"""
    global is_downloading, current_download_process, current_temp_files

    # Reset download state
    is_downloading = False
    current_download_process = None

    # Hide cancel button
    show_cancel_button(False)

    # Re-enable buttons
    set_button_states(analyze_enabled=True, download_enabled=True)

    # Show completion message
    if is_cancellation:
        # Cancellation is not an error, just a user action
        update_progress(message, progress=0)
    elif success:
        update_progress(message, progress=100)
    else:
        update_progress(message, error=True)

    if DEBUG_MODE:
        print(f"Download completed: {message}")

cancel_button = Button(window, text="✕", bg="#89003E", fg="#FFFFFF",
                      font=("Poppins", 12, "bold"), relief=FLAT, cursor="hand2",
                      activebackground="#672845", activeforeground="#FFFFFF",
                      command=cancel_download)
# Initially hidden - akan di-show saat download dimulai

def cleanup_and_exit():
    """Cleanup all processes before exiting"""
    if DEBUG_MODE:
        print("🧹 Cleaning up processes before exit...")

    # Terminate all active processes
    for process in active_processes[:]:  # Copy list to avoid modification during iteration
        try:
            if process.poll() is None:  # Process still running
                if DEBUG_MODE:
                    print(f"Terminating process: {process.pid}")
                process.terminate()
                # Give process time to terminate gracefully
                try:
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    # Force kill if doesn't terminate gracefully
                    process.kill()
                    if DEBUG_MODE:
                        print(f"Force killed process: {process.pid}")
            active_processes.remove(process)
        except Exception as e:
            if DEBUG_MODE:
                print(f"Error cleaning up process: {e}")

    # Wait for threads to finish (with timeout)
    for thread in active_threads[:]:
        if thread.is_alive():
            if DEBUG_MODE:
                print(f"Waiting for thread: {thread.name}")
            thread.join(timeout=2)  # Wait max 2 seconds
            if thread.is_alive():
                if DEBUG_MODE:
                    print(f"Thread still alive, forcing exit: {thread.name}")

    if DEBUG_MODE:
        print("✅ Cleanup completed")

    # Destroy window and exit
    window.destroy()

# Set cleanup handler for window close
window.protocol("WM_DELETE_WINDOW", cleanup_and_exit)

window.mainloop()
